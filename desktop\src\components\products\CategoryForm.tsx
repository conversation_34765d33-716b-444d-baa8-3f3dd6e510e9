import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Grid,
  Box,
  Typography,
  Alert,
  InputAdornment
} from '@mui/material'
import { CreateCategoryForm, UpdateCategoryForm, Category } from 'shared'
import { useCategoryStore } from '../../store/useCategoryStore'
import { FileUpload } from '../common/FileUpload'

// Validation schema
const categorySchema = z.object({
  parentId: z.string().optional(),
  name: z.string().min(1, 'Kategori adı zorunludur').max(100),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Geçerli bir hex renk kodu giriniz').optional(),
  icon: z.string().max(50).optional(),
  showInKitchen: z.boolean(),
  preparationTime: z.number().min(0).optional(),
  displayOrder: z.number().min(0),
  active: z.boolean(),
  showInMenu: z.boolean()
})

interface CategoryFormProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: CreateCategoryForm | UpdateCategoryForm) => Promise<void>
  category?: UpdateCategoryForm | null
  loading?: boolean
}

export const CategoryForm: React.FC<CategoryFormProps> = ({
  open,
  onClose,
  onSubmit,
  category,
  loading = false
}) => {
  const { t } = useTranslation()
  const [uploadedImage, setUploadedImage] = useState<File | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Category store
  const { categories, fetchCategories } = useCategoryStore()

  // Form setup
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    watch
  } = useForm<CreateCategoryForm>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      parentId: '',
      name: '',
      description: '',
      color: '#1976d2',
      icon: '',
      showInKitchen: true,
      preparationTime: 0,
      displayOrder: 0,
      active: true,
      showInMenu: true
    }
  })

  // Load categories on mount
  useEffect(() => {
    if (open) {
      fetchCategories({ active: true })
    }
  }, [open, fetchCategories])

  // Reset form when category changes
  useEffect(() => {
    if (category) {
      reset(category)
    } else {
      reset()
    }
    setUploadedImage(null)
    setError(null)
  }, [category, reset])

  // Handle form submission
  const handleFormSubmit = async (data: CreateCategoryForm) => {
    try {
      setError(null)
      const formData = {
        ...data,
        image: uploadedImage
      }
      await onSubmit(formData)
      onClose()
    } catch (err: any) {
      setError(err.message || 'Bir hata oluştu')
    }
  }

  // Handle file upload
  const handleFileUpload = (files: File[]) => {
    setUploadedImage(files[0] || null)
  }

  // Get available parent categories (exclude current category and its children)
  const getAvailableParentCategories = (): Category[] => {
    if (!category) return categories

    const excludeIds = new Set([category.id])
    
    // Add children recursively
    const addChildren = (parentId: string) => {
      categories.forEach(cat => {
        if (cat.parentId === parentId) {
          excludeIds.add(cat.id)
          addChildren(cat.id)
        }
      })
    }
    
    addChildren(category.id!)
    
    return categories.filter(cat => !excludeIds.has(cat.id))
  }

  // Predefined colors
  const predefinedColors = [
    '#1976d2', '#dc004e', '#9c27b0', '#673ab7',
    '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4',
    '#009688', '#4caf50', '#8bc34a', '#cddc39',
    '#ffeb3b', '#ffc107', '#ff9800', '#ff5722'
  ]

  // Common icons for categories
  const commonIcons = [
    'restaurant', 'local_pizza', 'local_cafe', 'local_bar',
    'cake', 'icecream', 'fastfood', 'lunch_dining',
    'dinner_dining', 'breakfast_dining', 'local_dining',
    'emoji_food_beverage', 'wine_bar', 'sports_bar'
  ]

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {category ? t('products.editCategory') : t('products.addCategory')}
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box component="form" sx={{ mt: 1 }}>
          <Grid container spacing={2}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('products.category.name')}
                    fullWidth
                    error={!!errors.name}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('products.category.description')}
                    fullWidth
                    multiline
                    rows={3}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="parentId"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>{t('products.category.parentCategory')}</InputLabel>
                    <Select {...field} label={t('products.category.parentCategory')}>
                      <MenuItem value="">
                        <em>{t('products.category.noParent')}</em>
                      </MenuItem>
                      {getAvailableParentCategories().map((cat) => (
                        <MenuItem key={cat.id} value={cat.id}>
                          {cat.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            {/* Visual Settings */}
            <Grid item xs={12} sm={6}>
              <Controller
                name="color"
                control={control}
                render={({ field }) => (
                  <Box>
                    <TextField
                      {...field}
                      label={t('products.category.color')}
                      fullWidth
                      error={!!errors.color}
                      helperText={errors.color?.message}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Box
                              sx={{
                                width: 24,
                                height: 24,
                                bgcolor: field.value || '#1976d2',
                                borderRadius: 1,
                                border: 1,
                                borderColor: 'divider'
                              }}
                            />
                          </InputAdornment>
                        )
                      }}
                    />
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                      {predefinedColors.map((color) => (
                        <Box
                          key={color}
                          sx={{
                            width: 24,
                            height: 24,
                            bgcolor: color,
                            borderRadius: 1,
                            border: field.value === color ? 2 : 1,
                            borderColor: field.value === color ? 'primary.main' : 'divider',
                            cursor: 'pointer'
                          }}
                          onClick={() => field.onChange(color)}
                        />
                      ))}
                    </Box>
                  </Box>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="icon"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>{t('products.category.icon')}</InputLabel>
                    <Select {...field} label={t('products.category.icon')}>
                      <MenuItem value="">
                        <em>İkon seçin</em>
                      </MenuItem>
                      {commonIcons.map((icon) => (
                        <MenuItem key={icon} value={icon}>
                          {icon}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            {/* Settings */}
            <Grid item xs={12} sm={6}>
              <Controller
                name="preparationTime"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('products.category.preparationTime')}
                    type="number"
                    fullWidth
                    InputProps={{
                      endAdornment: <InputAdornment position="end">dk</InputAdornment>
                    }}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="displayOrder"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('products.category.displayOrder')}
                    type="number"
                    fullWidth
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                )}
              />
            </Grid>

            {/* Switches */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Controller
                  name="active"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Switch {...field} checked={field.value} />}
                      label={t('products.category.active')}
                    />
                  )}
                />
                <Controller
                  name="showInMenu"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Switch {...field} checked={field.value} />}
                      label={t('products.category.showInMenu')}
                    />
                  )}
                />
                <Controller
                  name="showInKitchen"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Switch {...field} checked={field.value} />}
                      label={t('products.category.showInKitchen')}
                    />
                  )}
                />
              </Box>
            </Grid>

            {/* Image Upload */}
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                {t('products.category.uploadImage')}
              </Typography>
              <FileUpload
                onFilesChange={handleFileUpload}
                maxFiles={1}
                acceptedTypes={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}
                helperText="Kategori için tek resim yükleyebilirsiniz"
              />
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          {t('products.actions.cancel')}
        </Button>
        <Button
          onClick={handleSubmit(handleFormSubmit)}
          variant="contained"
          disabled={loading}
        >
          {t('products.actions.save')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

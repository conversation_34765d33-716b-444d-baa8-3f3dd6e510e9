import axios from 'axios'
import { 
  Product, 
  Category,
  ProductQuery, 
  CategoryQuery,
  CreateProductForm, 
  UpdateProductForm,
  CreateCategoryForm,
  UpdateCategoryForm,
  ProductListResponse,
  CategoryListResponse,
  BulkProductUpdate,
  ReorderCategories,
  ApiResponse 
} from 'shared'
import { API_ENDPOINTS } from 'shared'

// Axios instance with auth token
const api = axios.create({
  baseURL: '',
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired, redirect to login
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Product Service
export const productService = {
  // Get all products
  async getProducts(query?: ProductQuery): Promise<ProductListResponse> {
    const params = new URLSearchParams()
    if (query) {
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, String(value))
        }
      })
    }
    
    const response = await api.get(`${API_ENDPOINTS.PRODUCTS.LIST}?${params}`)
    return response.data.data
  },

  // Get single product
  async getProduct(id: string): Promise<Product> {
    const response = await api.get(API_ENDPOINTS.PRODUCTS.UPDATE.replace(':id', id))
    return response.data.data
  },

  // Create product
  async createProduct(data: CreateProductForm): Promise<Product> {
    const formData = new FormData()
    
    // Add text fields
    Object.entries(data).forEach(([key, value]) => {
      if (key === 'images') return // Handle separately
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          formData.append(key, JSON.stringify(value))
        } else {
          formData.append(key, String(value))
        }
      }
    })

    // Add image files
    if (data.images && data.images.length > 0) {
      data.images.forEach((file) => {
        formData.append('images', file)
      })
    }

    const response = await api.post(API_ENDPOINTS.PRODUCTS.CREATE, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data.data
  },

  // Update product
  async updateProduct(id: string, data: UpdateProductForm): Promise<Product> {
    const formData = new FormData()
    
    // Add text fields
    Object.entries(data).forEach(([key, value]) => {
      if (key === 'images' || key === 'id') return // Handle separately or skip
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          formData.append(key, JSON.stringify(value))
        } else {
          formData.append(key, String(value))
        }
      }
    })

    // Add image files
    if (data.images && data.images.length > 0) {
      data.images.forEach((file) => {
        formData.append('images', file)
      })
    }

    const response = await api.put(API_ENDPOINTS.PRODUCTS.UPDATE.replace(':id', id), formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data.data
  },

  // Delete product
  async deleteProduct(id: string): Promise<void> {
    await api.delete(API_ENDPOINTS.PRODUCTS.DELETE.replace(':id', id))
  },

  // Bulk update products
  async bulkUpdateProducts(data: BulkProductUpdate): Promise<void> {
    await api.patch(API_ENDPOINTS.PRODUCTS.BULK_UPDATE, data)
  }
}

// Category Service
export const categoryService = {
  // Get all categories
  async getCategories(query?: CategoryQuery): Promise<CategoryListResponse> {
    const params = new URLSearchParams()
    if (query) {
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, String(value))
        }
      })
    }
    
    const response = await api.get(`${API_ENDPOINTS.CATEGORIES.LIST}?${params}`)
    return response.data.data
  },

  // Get single category
  async getCategory(id: string): Promise<Category> {
    const response = await api.get(API_ENDPOINTS.CATEGORIES.UPDATE.replace(':id', id))
    return response.data.data
  },

  // Create category
  async createCategory(data: CreateCategoryForm): Promise<Category> {
    const formData = new FormData()
    
    // Add text fields
    Object.entries(data).forEach(([key, value]) => {
      if (key === 'image') return // Handle separately
      if (value !== undefined && value !== null) {
        formData.append(key, String(value))
      }
    })

    // Add image file
    if (data.image) {
      formData.append('image', data.image)
    }

    const response = await api.post(API_ENDPOINTS.CATEGORIES.CREATE, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data.data
  },

  // Update category
  async updateCategory(id: string, data: UpdateCategoryForm): Promise<Category> {
    const formData = new FormData()
    
    // Add text fields
    Object.entries(data).forEach(([key, value]) => {
      if (key === 'image' || key === 'id') return // Handle separately or skip
      if (value !== undefined && value !== null) {
        formData.append(key, String(value))
      }
    })

    // Add image file
    if (data.image) {
      formData.append('image', data.image)
    }

    const response = await api.put(API_ENDPOINTS.CATEGORIES.UPDATE.replace(':id', id), formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data.data
  },

  // Delete category
  async deleteCategory(id: string): Promise<void> {
    await api.delete(API_ENDPOINTS.CATEGORIES.DELETE.replace(':id', id))
  },

  // Reorder categories
  async reorderCategories(data: ReorderCategories): Promise<void> {
    await api.patch(API_ENDPOINTS.CATEGORIES.REORDER, data)
  }
}

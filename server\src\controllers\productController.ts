import { Request, Response } from 'express'
import prisma from '../lib/prisma'
import { 
  CreateProductInput, 
  UpdateProductInput, 
  ProductQueryInput,
  BulkProductUpdateInput,
  DuplicateProductInput
} from '../validators/productValidators'
import { getFileUrl, deleteFile } from '../middlewares/uploadMiddleware'

/**
 * Tüm ürünleri listele (pagination, filtering, sorting)
 */
export const getProducts = async (req: Request, res: Response) => {
  try {
    const query = req.query as unknown as ProductQueryInput
    const { page, limit, search, categoryId, active, showInMenu, type, minPrice, maxPrice, sortBy, sortOrder, featured, vegetarian, vegan, glutenFree } = query

    // Filtering conditions
    const where: any = {
      deletedAt: null, // Soft delete kontrolü
      companyId: req.user?.companyId // Multi-tenant
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (categoryId) where.categoryId = categoryId
    if (active !== undefined) where.active = active
    if (showInMenu !== undefined) where.showInMenu = showInMenu
    if (type) where.type = type
    if (featured !== undefined) where.featured = featured
    if (vegetarian !== undefined) where.vegetarian = vegetarian
    if (vegan !== undefined) where.vegan = vegan
    if (glutenFree !== undefined) where.glutenFree = glutenFree

    if (minPrice !== undefined || maxPrice !== undefined) {
      where.basePrice = {}
      if (minPrice !== undefined) where.basePrice.gte = minPrice
      if (maxPrice !== undefined) where.basePrice.lte = maxPrice
    }

    // Sorting
    const orderBy: any = {}
    orderBy[sortBy] = sortOrder

    // Pagination
    const skip = (page - 1) * limit

    // Execute queries
    const [products, totalCount] = await Promise.all([
      prisma.product.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true
            }
          },
          tax: {
            select: {
              id: true,
              name: true,
              rate: true
            }
          }
        }
      }),
      prisma.product.count({ where })
    ])

    // Format response with file URLs
    const formattedProducts = products.map(product => ({
      ...product,
      image: product.image ? getFileUrl(product.image) : null,
      images: product.images.map(img => getFileUrl(img))
    }))

    const totalPages = Math.ceil(totalCount / limit)

    res.json({
      success: true,
      data: {
        products: formattedProducts,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })
  } catch (error) {
    console.error('Get products error:', error)
    res.status(500).json({
      success: false,
      error: 'Ürünler alınırken hata oluştu'
    })
  }
}

/**
 * Tek ürün detayı getir
 */
export const getProduct = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    const product = await prisma.product.findFirst({
      where: {
        id,
        companyId: req.user?.companyId,
        deletedAt: null
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true
          }
        },
        tax: {
          select: {
            id: true,
            name: true,
            rate: true
          }
        },
        variants: {
          where: { deletedAt: null },
          orderBy: { displayOrder: 'asc' }
        },
        modifierGroups: {
          where: { deletedAt: null },
          include: {
            modifierGroup: {
              include: {
                modifiers: {
                  where: { deletedAt: null },
                  orderBy: { displayOrder: 'asc' }
                }
              }
            }
          }
        }
      }
    })

    if (!product) {
      return res.status(404).json({
        success: false,
        error: 'Ürün bulunamadı'
      })
    }

    // Format response with file URLs
    const formattedProduct = {
      ...product,
      image: product.image ? getFileUrl(product.image) : null,
      images: product.images.map(img => getFileUrl(img))
    }

    res.json({
      success: true,
      data: formattedProduct
    })
  } catch (error) {
    console.error('Get product error:', error)
    res.status(500).json({
      success: false,
      error: 'Ürün alınırken hata oluştu'
    })
  }
}

/**
 * Yeni ürün oluştur
 */
export const createProduct = async (req: Request, res: Response) => {
  try {
    const productData = req.body as CreateProductInput
    const files = req.files as Express.Multer.File[]
    const file = req.file as Express.Multer.File

    // Kategori kontrolü
    const category = await prisma.category.findFirst({
      where: {
        id: productData.categoryId,
        companyId: req.user?.companyId,
        deletedAt: null
      }
    })

    if (!category) {
      return res.status(400).json({
        success: false,
        error: 'Geçersiz kategori'
      })
    }

    // Vergi kontrolü
    const tax = await prisma.tax.findFirst({
      where: {
        id: productData.taxId,
        companyId: req.user?.companyId,
        deletedAt: null
      }
    })

    if (!tax) {
      return res.status(400).json({
        success: false,
        error: 'Geçersiz vergi oranı'
      })
    }

    // Ürün kodu benzersizlik kontrolü
    const existingProduct = await prisma.product.findFirst({
      where: {
        code: productData.code,
        companyId: req.user?.companyId,
        deletedAt: null
      }
    })

    if (existingProduct) {
      return res.status(400).json({
        success: false,
        error: 'Bu ürün kodu zaten kullanılıyor'
      })
    }

    // Dosya yollarını hazırla
    let imagePath: string | null = null
    let imagePaths: string[] = []

    if (file) {
      imagePath = `products/${file.filename}`
    }

    if (files && files.length > 0) {
      imagePaths = files.map(f => `products/${f.filename}`)
    }

    // Ürün oluştur
    const product = await prisma.product.create({
      data: {
        ...productData,
        companyId: req.user?.companyId!,
        image: imagePath,
        images: imagePaths,
        basePrice: Number(productData.basePrice),
        costPrice: productData.costPrice ? Number(productData.costPrice) : null,
        profitMargin: productData.profitMargin ? Number(productData.profitMargin) : null,
        minStockLevel: productData.minStockLevel ? Number(productData.minStockLevel) : null,
        maxStockLevel: productData.maxStockLevel ? Number(productData.maxStockLevel) : null,
        preparationTime: productData.preparationTime ? Number(productData.preparationTime) : null,
        calories: productData.calories ? Number(productData.calories) : null
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true
          }
        },
        tax: {
          select: {
            id: true,
            name: true,
            rate: true
          }
        }
      }
    })

    // Format response with file URLs
    const formattedProduct = {
      ...product,
      image: product.image ? getFileUrl(product.image) : null,
      images: product.images.map(img => getFileUrl(img))
    }

    res.status(201).json({
      success: true,
      data: formattedProduct,
      message: 'Ürün başarıyla oluşturuldu'
    })
  } catch (error) {
    console.error('Create product error:', error)
    res.status(500).json({
      success: false,
      error: 'Ürün oluşturulurken hata oluştu'
    })
  }
}

/**
 * Ürün güncelle
 */
export const updateProduct = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const updateData = req.body as UpdateProductInput
    const files = req.files as Express.Multer.File[]
    const file = req.file as Express.Multer.File

    // Mevcut ürünü kontrol et
    const existingProduct = await prisma.product.findFirst({
      where: {
        id,
        companyId: req.user?.companyId,
        deletedAt: null
      }
    })

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        error: 'Ürün bulunamadı'
      })
    }

    // Kategori kontrolü (eğer değiştiriliyorsa)
    if (updateData.categoryId && updateData.categoryId !== existingProduct.categoryId) {
      const category = await prisma.category.findFirst({
        where: {
          id: updateData.categoryId,
          companyId: req.user?.companyId,
          deletedAt: null
        }
      })

      if (!category) {
        return res.status(400).json({
          success: false,
          error: 'Geçersiz kategori'
        })
      }
    }

    // Vergi kontrolü (eğer değiştiriliyorsa)
    if (updateData.taxId && updateData.taxId !== existingProduct.taxId) {
      const tax = await prisma.tax.findFirst({
        where: {
          id: updateData.taxId,
          companyId: req.user?.companyId,
          deletedAt: null
        }
      })

      if (!tax) {
        return res.status(400).json({
          success: false,
          error: 'Geçersiz vergi oranı'
        })
      }
    }

    // Ürün kodu benzersizlik kontrolü (eğer değiştiriliyorsa)
    if (updateData.code && updateData.code !== existingProduct.code) {
      const codeExists = await prisma.product.findFirst({
        where: {
          code: updateData.code,
          companyId: req.user?.companyId,
          deletedAt: null,
          NOT: { id }
        }
      })

      if (codeExists) {
        return res.status(400).json({
          success: false,
          error: 'Bu ürün kodu zaten kullanılıyor'
        })
      }
    }

    // Dosya yollarını hazırla
    let imagePath: string | undefined = undefined
    let imagePaths: string[] | undefined = undefined

    if (file) {
      // Eski resmi sil
      if (existingProduct.image) {
        deleteFile(existingProduct.image)
      }
      imagePath = `products/${file.filename}`
    }

    if (files && files.length > 0) {
      // Eski resimleri sil
      existingProduct.images.forEach(img => deleteFile(img))
      imagePaths = files.map(f => `products/${f.filename}`)
    }

    // Numeric alanları dönüştür
    const processedUpdateData: any = { ...updateData }
    if (updateData.basePrice !== undefined) processedUpdateData.basePrice = Number(updateData.basePrice)
    if (updateData.costPrice !== undefined) processedUpdateData.costPrice = updateData.costPrice ? Number(updateData.costPrice) : null
    if (updateData.profitMargin !== undefined) processedUpdateData.profitMargin = updateData.profitMargin ? Number(updateData.profitMargin) : null
    if (updateData.minStockLevel !== undefined) processedUpdateData.minStockLevel = updateData.minStockLevel ? Number(updateData.minStockLevel) : null
    if (updateData.maxStockLevel !== undefined) processedUpdateData.maxStockLevel = updateData.maxStockLevel ? Number(updateData.maxStockLevel) : null
    if (updateData.preparationTime !== undefined) processedUpdateData.preparationTime = updateData.preparationTime ? Number(updateData.preparationTime) : null
    if (updateData.calories !== undefined) processedUpdateData.calories = updateData.calories ? Number(updateData.calories) : null

    // Resim alanlarını ekle
    if (imagePath !== undefined) processedUpdateData.image = imagePath
    if (imagePaths !== undefined) processedUpdateData.images = imagePaths

    // Ürünü güncelle
    const product = await prisma.product.update({
      where: { id },
      data: processedUpdateData,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true
          }
        },
        tax: {
          select: {
            id: true,
            name: true,
            rate: true
          }
        }
      }
    })

    // Format response with file URLs
    const formattedProduct = {
      ...product,
      image: product.image ? getFileUrl(product.image) : null,
      images: product.images.map(img => getFileUrl(img))
    }

    res.json({
      success: true,
      data: formattedProduct,
      message: 'Ürün başarıyla güncellendi'
    })
  } catch (error) {
    console.error('Update product error:', error)
    res.status(500).json({
      success: false,
      error: 'Ürün güncellenirken hata oluştu'
    })
  }
}

/**
 * Ürün sil (soft delete)
 */
export const deleteProduct = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    // Mevcut ürünü kontrol et
    const existingProduct = await prisma.product.findFirst({
      where: {
        id,
        companyId: req.user?.companyId,
        deletedAt: null
      }
    })

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        error: 'Ürün bulunamadı'
      })
    }

    // Aktif siparişlerde kullanılıp kullanılmadığını kontrol et
    const activeOrderItems = await prisma.orderItem.findFirst({
      where: {
        productId: id,
        order: {
          status: {
            in: ['PENDING', 'CONFIRMED', 'PREPARING', 'READY']
          }
        }
      }
    })

    if (activeOrderItems) {
      return res.status(400).json({
        success: false,
        error: 'Bu ürün aktif siparişlerde kullanıldığı için silinemez'
      })
    }

    // Soft delete
    await prisma.product.update({
      where: { id },
      data: {
        deletedAt: new Date(),
        active: false
      }
    })

    res.json({
      success: true,
      message: 'Ürün başarıyla silindi'
    })
  } catch (error) {
    console.error('Delete product error:', error)
    res.status(500).json({
      success: false,
      error: 'Ürün silinirken hata oluştu'
    })
  }
}

/**
 * Toplu ürün güncelleme
 */
export const bulkUpdateProducts = async (req: Request, res: Response) => {
  try {
    const { productIds, updates } = req.body as BulkProductUpdateInput

    // Ürünlerin varlığını kontrol et
    const existingProducts = await prisma.product.findMany({
      where: {
        id: { in: productIds },
        companyId: req.user?.companyId,
        deletedAt: null
      },
      select: { id: true }
    })

    if (existingProducts.length !== productIds.length) {
      return res.status(400).json({
        success: false,
        error: 'Bazı ürünler bulunamadı'
      })
    }

    // Kategori kontrolü (eğer değiştiriliyorsa)
    if (updates.categoryId) {
      const category = await prisma.category.findFirst({
        where: {
          id: updates.categoryId,
          companyId: req.user?.companyId,
          deletedAt: null
        }
      })

      if (!category) {
        return res.status(400).json({
          success: false,
          error: 'Geçersiz kategori'
        })
      }
    }

    // Toplu güncelleme
    await prisma.product.updateMany({
      where: {
        id: { in: productIds },
        companyId: req.user?.companyId
      },
      data: updates
    })

    res.json({
      success: true,
      message: `${productIds.length} ürün başarıyla güncellendi`
    })
  } catch (error) {
    console.error('Bulk update products error:', error)
    res.status(500).json({
      success: false,
      error: 'Ürünler güncellenirken hata oluştu'
    })
  }
}

export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    PROFILE: '/api/auth/profile'
  },
  ORDERS: {
    LIST: '/api/orders',
    CREATE: '/api/orders',
    UPDATE: '/api/orders/:id',
    DELETE: '/api/orders/:id'
  },
  PRODUCTS: {
    LIST: '/api/products',
    CREATE: '/api/products',
    UPDATE: '/api/products/:id',
    DELETE: '/api/products/:id',
    BULK_UPDATE: '/api/products/bulk'
  },
  CATEGORIES: {
    LIST: '/api/categories',
    CREATE: '/api/categories',
    UPDATE: '/api/categories/:id',
    DELETE: '/api/categories/:id',
    REORDER: '/api/categories/reorder'
  }
} as const

export const DEFAULT_PAGINATION = {
  PAGE: 1,
  LIMIT: 20,
  MAX_LIMIT: 100
} as const

export const CURRENCY = {
  SYMBOL: '₺',
  CODE: 'TRY',
  DECIMAL_PLACES: 2
} as const

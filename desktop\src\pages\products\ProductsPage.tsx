import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Fab,
  Tabs,
  Tab,
  Alert,
  CircularProgress
} from '@mui/material'
import {
  Search as SearchIcon,
  Add as AddIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Category as CategoryIcon
} from '@mui/icons-material'
import { useProductStore } from '../../store/useProductStore'
import { useCategoryStore } from '../../store/useCategoryStore'
import { Product, Category, CreateProductForm, UpdateProductForm, CreateCategoryForm, UpdateCategoryForm } from '@shared/types'
import { ProductForm } from '../../components/products/ProductForm'
import { CategoryForm } from '../../components/products/CategoryForm'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`products-tabpanel-${index}`}
      aria-labelledby={`products-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  )
}

export const ProductsPage: React.FC = () => {
  const { t } = useTranslation('products')
  const [tabValue, setTabValue] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [selectedCategoryForEdit, setSelectedCategoryForEdit] = useState<Category | null>(null)

  // Form states
  const [productFormOpen, setProductFormOpen] = useState(false)
  const [categoryFormOpen, setCategoryFormOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<UpdateProductForm | null>(null)
  const [editingCategory, setEditingCategory] = useState<UpdateCategoryForm | null>(null)

  // Store hooks
  const {
    products,
    loading: productsLoading,
    error: productsError,
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct
  } = useProductStore()

  const {
    categories,
    loading: categoriesLoading,
    error: categoriesError,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory
  } = useCategoryStore()

  // Load data on mount
  useEffect(() => {
    fetchProducts()
    fetchCategories({ hierarchical: true })
  }, [fetchProducts, fetchCategories])

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value)
    fetchProducts({ search: value, categoryId: selectedCategory || undefined })
  }

  // Handle category filter
  const handleCategoryFilter = (categoryId: string) => {
    setSelectedCategory(categoryId)
    fetchProducts({ search: searchTerm || undefined, categoryId: categoryId || undefined })
  }

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }

  // Handle menu
  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, product: Product) => {
    setAnchorEl(event.currentTarget)
    setSelectedProduct(product)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedProduct(null)
  }

  // Handle delete
  const handleDelete = async () => {
    if (selectedProduct) {
      try {
        await deleteProduct(selectedProduct.id)
        handleMenuClose()
      } catch (error) {
        console.error('Delete error:', error)
      }
    }
  }

  // Handle product form
  const handleProductSubmit = async (data: CreateProductForm | UpdateProductForm) => {
    if (editingProduct) {
      await updateProduct(editingProduct.id!, data as UpdateProductForm)
    } else {
      await createProduct(data as CreateProductForm)
    }
    setProductFormOpen(false)
    setEditingProduct(null)
  }

  // Handle category form
  const handleCategorySubmit = async (data: CreateCategoryForm | UpdateCategoryForm) => {
    if (editingCategory) {
      await updateCategory(editingCategory.id!, data as UpdateCategoryForm)
    } else {
      await createCategory(data as CreateCategoryForm)
    }
    setCategoryFormOpen(false)
    setEditingCategory(null)
  }

  // Handle edit product
  const handleEditProduct = (product: Product) => {
    setEditingProduct(product as UpdateProductForm)
    setProductFormOpen(true)
    handleMenuClose()
  }

  // Handle edit category
  const handleEditCategory = (category: Category) => {
    setEditingCategory(category as UpdateCategoryForm)
    setCategoryFormOpen(true)
  }

  // Handle add new
  const handleAddNew = () => {
    if (tabValue === 0) {
      setEditingProduct(null)
      setProductFormOpen(true)
    } else {
      setEditingCategory(null)
      setCategoryFormOpen(true)
    }
  }

  // Render product card
  const renderProductCard = (product: Product) => (
    <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {product.image && (
          <Box
            sx={{
              height: 200,
              backgroundImage: `url(${product.image})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              position: 'relative'
            }}
          >
            <IconButton
              sx={{ position: 'absolute', top: 8, right: 8, bgcolor: 'rgba(255,255,255,0.8)' }}
              onClick={(e) => handleMenuClick(e, product)}
            >
              <MoreVertIcon />
            </IconButton>
          </Box>
        )}
        <CardContent sx={{ flexGrow: 1 }}>
          <Typography variant="h6" component="h3" gutterBottom noWrap>
            {product.name}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {product.code}
          </Typography>
          <Typography variant="h6" color="primary" sx={{ mb: 1 }}>
            ₺{product.basePrice.toFixed(2)}
          </Typography>
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 1 }}>
            {product.featured && (
              <Chip label={t('products.filters.featured')} size="small" color="primary" />
            )}
            {product.vegetarian && (
              <Chip label={t('products.filters.vegetarian')} size="small" color="success" />
            )}
            {product.vegan && (
              <Chip label={t('products.filters.vegan')} size="small" color="success" />
            )}
            {product.glutenFree && (
              <Chip label={t('products.filters.glutenFree')} size="small" color="info" />
            )}
          </Box>
          <Chip
            label={product.active ? t('products.filters.active') : t('products.filters.inactive')}
            size="small"
            color={product.active ? 'success' : 'default'}
          />
        </CardContent>
      </Card>
    </Grid>
  )

  // Render category card
  const renderCategoryCard = (category: Category) => (
    <Grid item xs={12} sm={6} md={4} lg={3} key={category.id}>
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {category.image && (
          <Box
            sx={{
              height: 150,
              backgroundImage: `url(${category.image})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              position: 'relative'
            }}
          >
            <IconButton
              sx={{ position: 'absolute', top: 8, right: 8, bgcolor: 'rgba(255,255,255,0.8)' }}
              onClick={() => handleEditCategory(category)}
            >
              <EditIcon />
            </IconButton>
          </Box>
        )}
        <CardContent sx={{ flexGrow: 1 }}>
          <Typography variant="h6" component="h3" gutterBottom noWrap>
            {category.name}
          </Typography>
          {category.description && (
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              {category.description}
            </Typography>
          )}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Chip
              label={category.active ? t('products.filters.active') : t('products.filters.inactive')}
              size="small"
              color={category.active ? 'success' : 'default'}
            />
            {category.products && (
              <Typography variant="caption" color="text.secondary">
                {category.products.length} ürün
              </Typography>
            )}
          </Box>
        </CardContent>
      </Card>
    </Grid>
  )

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('title')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('subtitle')}
        </Typography>
      </Box>

      {/* Error Messages */}
      {(productsError || categoriesError) && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {productsError || categoriesError}
        </Alert>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label={t('menu.products')} />
          <Tab label={t('menu.categories')} />
        </Tabs>
      </Box>

      {/* Search and Filters */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
        <TextField
          placeholder={t('searchPlaceholder')}
          value={searchTerm}
          onChange={(e) => handleSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            )
          }}
          sx={{ minWidth: 300 }}
        />
        <Button startIcon={<FilterIcon />} variant="outlined">
          {t('filters.category')}
        </Button>
      </Box>

      {/* Content */}
      <TabPanel value={tabValue} index={0}>
        {productsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : products.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h6" color="text.secondary">
              {t('products.noProducts')}
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={2}>
            {products.map(renderProductCard)}
          </Grid>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {categoriesLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : categories.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h6" color="text.secondary">
              {t('products.noCategories')}
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={2}>
            {categories.map(renderCategoryCard)}
          </Grid>
        )}
      </TabPanel>

      {/* FAB */}
      <Fab
        color="primary"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={handleAddNew}
      >
        <AddIcon />
      </Fab>

      {/* Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedProduct && handleEditProduct(selectedProduct)}>
          <EditIcon sx={{ mr: 1 }} />
          {t('products.actions.edit')}
        </MenuItem>
        <MenuItem onClick={handleDelete}>
          <DeleteIcon sx={{ mr: 1 }} />
          {t('products.actions.delete')}
        </MenuItem>
      </Menu>

      {/* Forms */}
      <ProductForm
        open={productFormOpen}
        onClose={() => {
          setProductFormOpen(false)
          setEditingProduct(null)
        }}
        onSubmit={handleProductSubmit}
        product={editingProduct}
        loading={productsLoading}
      />

      <CategoryForm
        open={categoryFormOpen}
        onClose={() => {
          setCategoryFormOpen(false)
          setEditingCategory(null)
        }}
        onSubmit={handleCategorySubmit}
        category={editingCategory}
        loading={categoriesLoading}
      />
    </Box>
  )
}

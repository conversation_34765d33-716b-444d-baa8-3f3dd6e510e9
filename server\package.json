{"name": "restoran-pos-server", "version": "1.0.0", "type": "module", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec tsx src/index.ts", "build": "tsc", "start": "node --require tsconfig-paths/register dist/index.js", "lint": "eslint src --ext .ts", "test": "jest", "migrate": "prisma migrate dev", "generate": "prisma generate", "studio": "prisma studio", "seed": "tsx ../prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/multer": "^2.0.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "socket.io": "^4.8.1", "winston": "^3.17.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.13", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "jest": "^30.0.4", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-jest": "^29.4.0", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0"}}
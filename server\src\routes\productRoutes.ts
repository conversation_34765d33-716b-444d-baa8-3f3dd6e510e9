import { Router } from 'express'
import {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  bulkUpdateProducts
} from '../controllers/productController'
import { authenticateToken } from '../middlewares/authMiddleware'
import { validate, validateQuery, validateParams } from '../middlewares/validationMiddleware'
import { uploadSingle, uploadMultiple, handleUploadError } from '../middlewares/uploadMiddleware'
import {
  CreateProductSchema,
  UpdateProductSchema,
  ProductQuerySchema,
  ProductIdSchema,
  BulkProductUpdateSchema
} from '../validators/productValidators'

const router = Router()

// Tüm routes için authentication gerekli
router.use(authenticateToken)

/**
 * GET /api/products
 * Tüm ürünleri listele (pagination, filtering, sorting)
 */
router.get('/',
  validateQuery(ProductQuerySchema),
  getProducts
)

/**
 * GET /api/products/:id
 * Tek ürün detayı getir
 */
router.get('/:id',
  validateParams(ProductIdSchema),
  getProduct
)

/**
 * POST /api/products
 * Yeni ürün oluştur (resim yükleme ile)
 */
router.post('/',
  uploadMultiple('images', 5), // Maksimum 5 resim
  handleUploadError,
  validate(CreateProductSchema),
  createProduct
)

/**
 * PUT /api/products/:id
 * Ürün güncelle (resim yükleme ile)
 */
router.put('/:id',
  validateParams(ProductIdSchema),
  uploadMultiple('images', 5), // Maksimum 5 resim
  handleUploadError,
  validate(UpdateProductSchema),
  updateProduct
)

/**
 * DELETE /api/products/:id
 * Ürün sil (soft delete)
 */
router.delete('/:id',
  validateParams(ProductIdSchema),
  deleteProduct
)

/**
 * PATCH /api/products/bulk
 * Toplu ürün güncelleme
 */
router.patch('/bulk',
  validate(BulkProductUpdateSchema),
  bulkUpdateProducts
)

export default router

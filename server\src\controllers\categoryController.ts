import { Request, Response } from 'express'
import prisma from '../lib/prisma'
import { 
  CreateCategoryInput, 
  UpdateCategoryInput, 
  CategoryQueryInput,
  CategoryHierarchyInput,
  BulkCategoryUpdateInput,
  MoveCategoryInput,
  ReorderCategoriesInput
} from '../validators/categoryValidators'
import { getFileUrl, deleteFile } from '../middlewares/uploadMiddleware'

/**
 * Tüm kategorileri listele (pagination, filtering, sorting)
 */
export const getCategories = async (req: Request, res: Response) => {
  try {
    const query = req.query as unknown as CategoryQueryInput
    const { page, limit, search, parentId, active, showInMenu, showInKitchen, sortBy, sortOrder, includeProducts, includeChildren, hierarchical } = query

    // Filtering conditions
    const where: any = {
      deletedAt: null, // Soft delete kontrolü
      companyId: req.user?.companyId // Multi-tenant
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (parentId !== undefined) {
      where.parentId = parentId === 'null' ? null : parentId
    }
    if (active !== undefined) where.active = active
    if (showInMenu !== undefined) where.showInMenu = showInMenu
    if (showInKitchen !== undefined) where.showInKitchen = showInKitchen

    // Hiyerarşik yapı için özel işlem
    if (hierarchical) {
      const categories = await prisma.category.findMany({
        where: {
          ...where,
          parentId: null // Sadece root kategoriler
        },
        orderBy: { [sortBy]: sortOrder },
        include: {
          children: includeChildren ? {
            where: { deletedAt: null },
            orderBy: { displayOrder: 'asc' },
            include: {
              children: {
                where: { deletedAt: null },
                orderBy: { displayOrder: 'asc' }
              },
              products: includeProducts ? {
                where: { deletedAt: null, active: true },
                select: {
                  id: true,
                  name: true,
                  basePrice: true,
                  image: true
                }
              } : false
            }
          } : false,
          products: includeProducts ? {
            where: { deletedAt: null, active: true },
            select: {
              id: true,
              name: true,
              basePrice: true,
              image: true
            }
          } : false
        }
      })

      // Format response with file URLs
      const formattedCategories = categories.map(category => formatCategoryWithFiles(category))

      return res.json({
        success: true,
        data: {
          categories: formattedCategories,
          hierarchical: true
        }
      })
    }

    // Normal pagination
    const orderBy: any = {}
    orderBy[sortBy] = sortOrder

    const skip = (page - 1) * limit

    // Include options
    const include: any = {}
    if (includeProducts) {
      include.products = {
        where: { deletedAt: null, active: true },
        select: {
          id: true,
          name: true,
          basePrice: true,
          image: true
        }
      }
    }
    if (includeChildren) {
      include.children = {
        where: { deletedAt: null },
        orderBy: { displayOrder: 'asc' }
      }
    }

    // Execute queries
    const [categories, totalCount] = await Promise.all([
      prisma.category.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        include
      }),
      prisma.category.count({ where })
    ])

    // Format response with file URLs
    const formattedCategories = categories.map(category => formatCategoryWithFiles(category))

    const totalPages = Math.ceil(totalCount / limit)

    res.json({
      success: true,
      data: {
        categories: formattedCategories,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    })
  } catch (error) {
    console.error('Get categories error:', error)
    res.status(500).json({
      success: false,
      error: 'Kategoriler alınırken hata oluştu'
    })
  }
}

/**
 * Tek kategori detayı getir
 */
export const getCategory = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    const category = await prisma.category.findFirst({
      where: {
        id,
        companyId: req.user?.companyId,
        deletedAt: null
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true
          }
        },
        children: {
          where: { deletedAt: null },
          orderBy: { displayOrder: 'asc' },
          select: {
            id: true,
            name: true,
            color: true,
            icon: true,
            displayOrder: true,
            active: true
          }
        },
        products: {
          where: { deletedAt: null },
          orderBy: { displayOrder: 'asc' },
          select: {
            id: true,
            name: true,
            basePrice: true,
            image: true,
            active: true,
            showInMenu: true
          }
        }
      }
    })

    if (!category) {
      return res.status(404).json({
        success: false,
        error: 'Kategori bulunamadı'
      })
    }

    // Format response with file URLs
    const formattedCategory = formatCategoryWithFiles(category)

    res.json({
      success: true,
      data: formattedCategory
    })
  } catch (error) {
    console.error('Get category error:', error)
    res.status(500).json({
      success: false,
      error: 'Kategori alınırken hata oluştu'
    })
  }
}

// Helper function to format category with file URLs
const formatCategoryWithFiles = (category: any): any => {
  const formatted = {
    ...category,
    image: category.image ? getFileUrl(category.image) : null
  }

  if (category.children) {
    formatted.children = category.children.map((child: any) => formatCategoryWithFiles(child))
  }

  if (category.products) {
    formatted.products = category.products.map((product: any) => ({
      ...product,
      image: product.image ? getFileUrl(product.image) : null
    }))
  }

  return formatted
}

/**
 * Yeni kategori oluştur
 */
export const createCategory = async (req: Request, res: Response) => {
  try {
    const categoryData = req.body as CreateCategoryInput
    const file = req.file as Express.Multer.File

    // Parent kategori kontrolü (eğer varsa)
    if (categoryData.parentId) {
      const parentCategory = await prisma.category.findFirst({
        where: {
          id: categoryData.parentId,
          companyId: req.user?.companyId,
          deletedAt: null
        }
      })

      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          error: 'Geçersiz üst kategori'
        })
      }

      // Circular reference kontrolü (parent'ın parent'ı bu kategori olmamalı)
      let currentParent = parentCategory
      let depth = 0
      while (currentParent.parentId && depth < 10) {
        const nextParent = await prisma.category.findFirst({
          where: { id: currentParent.parentId, deletedAt: null }
        })
        if (!nextParent) break
        currentParent = nextParent
        depth++
      }

      if (depth >= 10) {
        return res.status(400).json({
          success: false,
          error: 'Kategori hiyerarşisi çok derin'
        })
      }
    }

    // Aynı isimde kategori kontrolü (aynı parent altında)
    const existingCategory = await prisma.category.findFirst({
      where: {
        name: categoryData.name,
        parentId: categoryData.parentId || null,
        companyId: req.user?.companyId,
        deletedAt: null
      }
    })

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        error: 'Bu isimde bir kategori zaten mevcut'
      })
    }

    // Dosya yolunu hazırla
    let imagePath: string | null = null
    if (file) {
      imagePath = `categories/${file.filename}`
    }

    // Kategori oluştur
    const category = await prisma.category.create({
      data: {
        ...categoryData,
        companyId: req.user?.companyId!,
        image: imagePath,
        preparationTime: categoryData.preparationTime ? Number(categoryData.preparationTime) : null
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true
          }
        }
      }
    })

    // Format response with file URLs
    const formattedCategory = formatCategoryWithFiles(category)

    res.status(201).json({
      success: true,
      data: formattedCategory,
      message: 'Kategori başarıyla oluşturuldu'
    })
  } catch (error) {
    console.error('Create category error:', error)
    res.status(500).json({
      success: false,
      error: 'Kategori oluşturulurken hata oluştu'
    })
  }
}

/**
 * Kategori güncelle
 */
export const updateCategory = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const updateData = req.body as UpdateCategoryInput
    const file = req.file as Express.Multer.File

    // Mevcut kategoriyi kontrol et
    const existingCategory = await prisma.category.findFirst({
      where: {
        id,
        companyId: req.user?.companyId,
        deletedAt: null
      }
    })

    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        error: 'Kategori bulunamadı'
      })
    }

    // Parent kategori kontrolü (eğer değiştiriliyorsa)
    if (updateData.parentId !== undefined && updateData.parentId !== existingCategory.parentId) {
      if (updateData.parentId) {
        // Kendi kendisine parent olamaz
        if (updateData.parentId === id) {
          return res.status(400).json({
            success: false,
            error: 'Kategori kendi kendisine üst kategori olamaz'
          })
        }

        const parentCategory = await prisma.category.findFirst({
          where: {
            id: updateData.parentId,
            companyId: req.user?.companyId,
            deletedAt: null
          }
        })

        if (!parentCategory) {
          return res.status(400).json({
            success: false,
            error: 'Geçersiz üst kategori'
          })
        }

        // Circular reference kontrolü
        let currentParent = parentCategory
        let depth = 0
        while (currentParent.parentId && depth < 10) {
          if (currentParent.parentId === id) {
            return res.status(400).json({
              success: false,
              error: 'Bu işlem döngüsel referans oluşturacak'
            })
          }
          const nextParent = await prisma.category.findFirst({
            where: { id: currentParent.parentId, deletedAt: null }
          })
          if (!nextParent) break
          currentParent = nextParent
          depth++
        }
      }
    }

    // Aynı isimde kategori kontrolü (eğer isim değiştiriliyorsa)
    if (updateData.name && updateData.name !== existingCategory.name) {
      const nameExists = await prisma.category.findFirst({
        where: {
          name: updateData.name,
          parentId: updateData.parentId !== undefined ? updateData.parentId : existingCategory.parentId,
          companyId: req.user?.companyId,
          deletedAt: null,
          NOT: { id }
        }
      })

      if (nameExists) {
        return res.status(400).json({
          success: false,
          error: 'Bu isimde bir kategori zaten mevcut'
        })
      }
    }

    // Dosya yolunu hazırla
    let imagePath: string | undefined = undefined
    if (file) {
      // Eski resmi sil
      if (existingCategory.image) {
        deleteFile(existingCategory.image)
      }
      imagePath = `categories/${file.filename}`
    }

    // Numeric alanları dönüştür
    const processedUpdateData: any = { ...updateData }
    if (updateData.preparationTime !== undefined) {
      processedUpdateData.preparationTime = updateData.preparationTime ? Number(updateData.preparationTime) : null
    }

    // Resim alanını ekle
    if (imagePath !== undefined) {
      processedUpdateData.image = imagePath
    }

    // Kategoriyi güncelle
    const category = await prisma.category.update({
      where: { id },
      data: processedUpdateData,
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true
          }
        },
        children: {
          where: { deletedAt: null },
          orderBy: { displayOrder: 'asc' },
          select: {
            id: true,
            name: true,
            color: true,
            icon: true
          }
        }
      }
    })

    // Format response with file URLs
    const formattedCategory = formatCategoryWithFiles(category)

    res.json({
      success: true,
      data: formattedCategory,
      message: 'Kategori başarıyla güncellendi'
    })
  } catch (error) {
    console.error('Update category error:', error)
    res.status(500).json({
      success: false,
      error: 'Kategori güncellenirken hata oluştu'
    })
  }
}

/**
 * Kategori sil (soft delete)
 */
export const deleteCategory = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    // Mevcut kategoriyi kontrol et
    const existingCategory = await prisma.category.findFirst({
      where: {
        id,
        companyId: req.user?.companyId,
        deletedAt: null
      },
      include: {
        children: {
          where: { deletedAt: null }
        },
        products: {
          where: { deletedAt: null }
        }
      }
    })

    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        error: 'Kategori bulunamadı'
      })
    }

    // Alt kategoriler varsa silinemez
    if (existingCategory.children.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Bu kategorinin alt kategorileri bulunduğu için silinemez'
      })
    }

    // Ürünler varsa silinemez
    if (existingCategory.products.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Bu kategoride ürünler bulunduğu için silinemez'
      })
    }

    // Soft delete
    await prisma.category.update({
      where: { id },
      data: {
        deletedAt: new Date(),
        active: false
      }
    })

    res.json({
      success: true,
      message: 'Kategori başarıyla silindi'
    })
  } catch (error) {
    console.error('Delete category error:', error)
    res.status(500).json({
      success: false,
      error: 'Kategori silinirken hata oluştu'
    })
  }
}

/**
 * Kategori sıralama güncelle
 */
export const reorderCategories = async (req: Request, res: Response) => {
  try {
    const { categories } = req.body as ReorderCategoriesInput

    // Kategorilerin varlığını kontrol et
    const existingCategories = await prisma.category.findMany({
      where: {
        id: { in: categories.map(c => c.id) },
        companyId: req.user?.companyId,
        deletedAt: null
      },
      select: { id: true }
    })

    if (existingCategories.length !== categories.length) {
      return res.status(400).json({
        success: false,
        error: 'Bazı kategoriler bulunamadı'
      })
    }

    // Transaction ile sıralama güncelle
    await prisma.$transaction(
      categories.map(category =>
        prisma.category.update({
          where: { id: category.id },
          data: { displayOrder: category.displayOrder }
        })
      )
    )

    res.json({
      success: true,
      message: 'Kategori sıralaması başarıyla güncellendi'
    })
  } catch (error) {
    console.error('Reorder categories error:', error)
    res.status(500).json({
      success: false,
      error: 'Kategori sıralaması güncellenirken hata oluştu'
    })
  }
}

// Product Types
export interface Product {
  id: string
  companyId: string
  categoryId: string
  code: string
  barcode?: string | null
  name: string
  description?: string | null
  shortDescription?: string | null
  image?: string | null
  images: string[]
  basePrice: number
  taxId: string
  costPrice?: number | null
  profitMargin?: number | null
  unit: ProductUnit
  type: ProductType
  trackStock: boolean
  allowNegativeStock: boolean
  minStockLevel?: number | null
  maxStockLevel?: number | null
  preparationTime?: number | null
  calories?: number | null
  allergens: string[]
  tags: string[]
  active: boolean
  showInMenu: boolean
  showInKitchen: boolean
  allowOnlineOrdering: boolean
  displayOrder: number
  featured: boolean
  spicy: boolean
  vegetarian: boolean
  vegan: boolean
  glutenFree: boolean
  organic: boolean
  version: number
  createdAt: string
  updatedAt: string
  deletedAt?: string | null
  
  // Relations
  category?: Category
  tax?: Tax
  variants?: ProductVariant[]
  modifierGroups?: ProductModifierGroup[]
}

export interface Category {
  id: string
  companyId: string
  parentId?: string | null
  name: string
  description?: string | null
  image?: string | null
  color?: string | null
  icon?: string | null
  showInKitchen: boolean
  preparationTime?: number | null
  displayOrder: number
  active: boolean
  showInMenu: boolean
  version: number
  createdAt: string
  updatedAt: string
  deletedAt?: string | null
  
  // Relations
  parent?: Category | null
  children?: Category[]
  products?: Product[]
}

export interface Tax {
  id: string
  companyId: string
  name: string
  rate: number
  active: boolean
  createdAt: string
  updatedAt: string
}

export interface ProductVariant {
  id: string
  productId: string
  name: string
  price: number
  active: boolean
  displayOrder: number
}

export interface ProductModifierGroup {
  id: string
  productId: string
  modifierGroupId: string
  required: boolean
  minSelection: number
  maxSelection: number
}

// Enums
export enum ProductUnit {
  PIECE = 'PIECE',
  KG = 'KG',
  GRAM = 'GRAM',
  LITER = 'LITER',
  ML = 'ML',
  PORTION = 'PORTION',
  BOX = 'BOX',
  PACKAGE = 'PACKAGE'
}

export enum ProductType {
  SIMPLE = 'SIMPLE',
  VARIANT = 'VARIANT',
  COMBO = 'COMBO',
  RECIPE = 'RECIPE'
}

// Form Types
export interface CreateProductForm {
  categoryId: string
  code: string
  barcode?: string
  name: string
  description?: string
  shortDescription?: string
  basePrice: number
  taxId: string
  costPrice?: number
  profitMargin?: number
  unit: ProductUnit
  type: ProductType
  trackStock: boolean
  allowNegativeStock: boolean
  minStockLevel?: number
  maxStockLevel?: number
  preparationTime?: number
  calories?: number
  allergens: string[]
  tags: string[]
  active: boolean
  showInMenu: boolean
  showInKitchen: boolean
  allowOnlineOrdering: boolean
  displayOrder: number
  featured: boolean
  spicy: boolean
  vegetarian: boolean
  vegan: boolean
  glutenFree: boolean
  organic: boolean
  images?: File[]
}

export interface UpdateProductForm extends Partial<CreateProductForm> {
  id: string
}

export interface CreateCategoryForm {
  parentId?: string
  name: string
  description?: string
  color?: string
  icon?: string
  showInKitchen: boolean
  preparationTime?: number
  displayOrder: number
  active: boolean
  showInMenu: boolean
  image?: File
}

export interface UpdateCategoryForm extends Partial<CreateCategoryForm> {
  id: string
}

// Query Types
export interface ProductQuery {
  page?: number
  limit?: number
  search?: string
  categoryId?: string
  active?: boolean
  showInMenu?: boolean
  type?: ProductType
  minPrice?: number
  maxPrice?: number
  sortBy?: 'name' | 'basePrice' | 'createdAt' | 'updatedAt' | 'displayOrder'
  sortOrder?: 'asc' | 'desc'
  featured?: boolean
  vegetarian?: boolean
  vegan?: boolean
  glutenFree?: boolean
}

export interface CategoryQuery {
  page?: number
  limit?: number
  search?: string
  parentId?: string | null
  active?: boolean
  showInMenu?: boolean
  showInKitchen?: boolean
  sortBy?: 'name' | 'displayOrder' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
  includeProducts?: boolean
  includeChildren?: boolean
  hierarchical?: boolean
}

// Response Types
export interface ProductListResponse {
  products: Product[]
  pagination: {
    page: number
    limit: number
    totalCount: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface CategoryListResponse {
  categories: Category[]
  pagination?: {
    page: number
    limit: number
    totalCount: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  hierarchical?: boolean
}

// Bulk Operations
export interface BulkProductUpdate {
  productIds: string[]
  updates: {
    active?: boolean
    showInMenu?: boolean
    showInKitchen?: boolean
    categoryId?: string
    displayOrder?: number
  }
}

export interface ReorderCategories {
  categories: {
    id: string
    displayOrder: number
  }[]
}

import { Request, Response, NextFunction } from 'express'
import { z } from 'zod'

/**
 * Zod schema validation middleware
 */
export const validate = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Request body'yi validate et
      const validatedData = schema.parse(req.body)
      
      // Validated data'yı request'e ekle
      req.body = validatedData
      
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code
        }))
        
        return res.status(400).json({
          success: false,
          error: 'Validation hatası',
          details: errors
        })
      }
      
      // Diğer hatalar için
      return res.status(400).json({
        success: false,
        error: 'Geçersiz veri formatı'
      })
    }
  }
}

/**
 * Query parameters validation middleware
 */
export const validateQuery = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Query parameters'ı validate et
      const validatedQuery = schema.parse(req.query)
      
      // Validated query'yi request'e ekle
      req.query = validatedQuery as any
      
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code
        }))
        
        return res.status(400).json({
          success: false,
          error: 'Query validation hatası',
          details: errors
        })
      }
      
      return res.status(400).json({
        success: false,
        error: 'Geçersiz query parametreleri'
      })
    }
  }
}

/**
 * URL parameters validation middleware
 */
export const validateParams = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // URL parameters'ı validate et
      const validatedParams = schema.parse(req.params)
      
      // Validated params'ı request'e ekle
      req.params = validatedParams as any
      
      next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code
        }))
        
        return res.status(400).json({
          success: false,
          error: 'Parameter validation hatası',
          details: errors
        })
      }
      
      return res.status(400).json({
        success: false,
        error: 'Geçersiz URL parametreleri'
      })
    }
  }
}

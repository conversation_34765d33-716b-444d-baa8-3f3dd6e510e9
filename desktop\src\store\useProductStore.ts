import { create } from 'zustand'
import { 
  Product, 
  ProductQuery, 
  CreateProductForm, 
  UpdateProductForm,
  BulkProductUpdate,
  ProductListResponse 
} from 'shared'
import { productService } from '../services/productService'

interface ProductState {
  // State
  products: Product[]
  selectedProduct: Product | null
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    totalCount: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  } | null

  // Actions
  fetchProducts: (query?: ProductQuery) => Promise<void>
  fetchProduct: (id: string) => Promise<void>
  createProduct: (data: CreateProductForm) => Promise<Product>
  updateProduct: (id: string, data: UpdateProductForm) => Promise<Product>
  deleteProduct: (id: string) => Promise<void>
  bulkUpdateProducts: (data: BulkProductUpdate) => Promise<void>
  setSelectedProduct: (product: Product | null) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useProductStore = create<ProductState>((set, get) => ({
  // Initial State
  products: [],
  selectedProduct: null,
  loading: false,
  error: null,
  pagination: null,

  // Actions
  fetchProducts: async (query?: ProductQuery) => {
    set({ loading: true, error: null })
    try {
      const response: ProductListResponse = await productService.getProducts(query)
      set({ 
        products: response.products,
        pagination: response.pagination,
        loading: false 
      })
    } catch (error: any) {
      set({ 
        error: error.response?.data?.error || 'Ürünler yüklenirken hata oluştu',
        loading: false 
      })
    }
  },

  fetchProduct: async (id: string) => {
    set({ loading: true, error: null })
    try {
      const product = await productService.getProduct(id)
      set({ 
        selectedProduct: product,
        loading: false 
      })
    } catch (error: any) {
      set({ 
        error: error.response?.data?.error || 'Ürün yüklenirken hata oluştu',
        loading: false 
      })
    }
  },

  createProduct: async (data: CreateProductForm) => {
    set({ loading: true, error: null })
    try {
      const product = await productService.createProduct(data)
      
      // Add to products list
      set(state => ({ 
        products: [product, ...state.products],
        loading: false 
      }))
      
      return product
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Ürün oluşturulurken hata oluştu'
      set({ 
        error: errorMessage,
        loading: false 
      })
      throw new Error(errorMessage)
    }
  },

  updateProduct: async (id: string, data: UpdateProductForm) => {
    set({ loading: true, error: null })
    try {
      const product = await productService.updateProduct(id, data)
      
      // Update in products list
      set(state => ({ 
        products: state.products.map(p => p.id === id ? product : p),
        selectedProduct: state.selectedProduct?.id === id ? product : state.selectedProduct,
        loading: false 
      }))
      
      return product
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Ürün güncellenirken hata oluştu'
      set({ 
        error: errorMessage,
        loading: false 
      })
      throw new Error(errorMessage)
    }
  },

  deleteProduct: async (id: string) => {
    set({ loading: true, error: null })
    try {
      await productService.deleteProduct(id)
      
      // Remove from products list
      set(state => ({ 
        products: state.products.filter(p => p.id !== id),
        selectedProduct: state.selectedProduct?.id === id ? null : state.selectedProduct,
        loading: false 
      }))
    } catch (error: any) {
      set({ 
        error: error.response?.data?.error || 'Ürün silinirken hata oluştu',
        loading: false 
      })
    }
  },

  bulkUpdateProducts: async (data: BulkProductUpdate) => {
    set({ loading: true, error: null })
    try {
      await productService.bulkUpdateProducts(data)
      
      // Refresh products list
      const { fetchProducts } = get()
      await fetchProducts()
    } catch (error: any) {
      set({ 
        error: error.response?.data?.error || 'Toplu güncelleme sırasında hata oluştu',
        loading: false 
      })
    }
  },

  setSelectedProduct: (product: Product | null) => {
    set({ selectedProduct: product })
  },

  clearError: () => {
    set({ error: null })
  },

  setLoading: (loading: boolean) => {
    set({ loading })
  }
}))

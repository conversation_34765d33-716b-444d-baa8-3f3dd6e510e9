import multer from 'multer'
import path from 'path'
import fs from 'fs'
import { Request, Response, NextFunction } from 'express'

// Upload klasörünü oluştur
const uploadDir = process.env.UPLOAD_PATH || './uploads'
const productsUploadDir = path.join(uploadDir, 'products')
const categoriesUploadDir = path.join(uploadDir, 'categories')

// Klasörleri oluştur
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true })
}
if (!fs.existsSync(productsUploadDir)) {
  fs.mkdirSync(productsUploadDir, { recursive: true })
}
if (!fs.existsSync(categoriesUploadDir)) {
  fs.mkdirSync(categoriesUploadDir, { recursive: true })
}

// Dosya adı oluşturma
const generateFileName = (originalname: string): string => {
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 15)
  const extension = path.extname(originalname)
  return `${timestamp}-${randomString}${extension}`
}

// Multer storage configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Request path'e göre klasör belirleme
    let uploadPath = uploadDir
    
    if (req.path.includes('/products')) {
      uploadPath = productsUploadDir
    } else if (req.path.includes('/categories')) {
      uploadPath = categoriesUploadDir
    }
    
    cb(null, uploadPath)
  },
  filename: (req, file, cb) => {
    const fileName = generateFileName(file.originalname)
    cb(null, fileName)
  }
})

// Dosya filtreleme
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Sadece resim dosyalarına izin ver
  const allowedMimeTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ]
  
  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true)
  } else {
    cb(new Error('Sadece resim dosyaları yüklenebilir (JPEG, PNG, GIF, WebP)'))
  }
}

// Multer configuration
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB default
    files: 5 // Maksimum 5 dosya
  }
})

// Single file upload middleware
export const uploadSingle = (fieldName: string = 'image') => {
  return upload.single(fieldName)
}

// Multiple files upload middleware
export const uploadMultiple = (fieldName: string = 'images', maxCount: number = 5) => {
  return upload.array(fieldName, maxCount)
}

// Error handling middleware for multer
export const handleUploadError = (err: any, req: Request, res: Response, next: NextFunction) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'Dosya boyutu çok büyük. Maksimum 10MB yükleyebilirsiniz.'
      })
    }
    if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Çok fazla dosya. Maksimum 5 dosya yükleyebilirsiniz.'
      })
    }
    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        error: 'Beklenmeyen dosya alanı.'
      })
    }
  }
  
  if (err.message) {
    return res.status(400).json({
      success: false,
      error: err.message
    })
  }
  
  next(err)
}

// Dosya silme utility
export const deleteFile = (filePath: string): boolean => {
  try {
    const fullPath = path.join(uploadDir, filePath)
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath)
      return true
    }
    return false
  } catch (error) {
    console.error('Dosya silme hatası:', error)
    return false
  }
}

// Dosya URL'i oluşturma utility
export const getFileUrl = (filePath: string): string => {
  if (!filePath) return ''
  return `/uploads/${filePath}`
}

// Static files middleware için uploads klasörünü serve etme
export const serveUploads = (app: any) => {
  const express = require('express')

  app.use('/uploads', (req: Request, res: Response, next: NextFunction) => {
    // CORS headers for images
    res.header('Access-Control-Allow-Origin', '*')
    res.header('Access-Control-Allow-Methods', 'GET')
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept')
    next()
  })

  app.use('/uploads', express.static(uploadDir))
}

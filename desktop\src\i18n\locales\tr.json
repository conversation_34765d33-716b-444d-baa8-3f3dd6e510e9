{"app": {"title": "Restoran POS Sistemi", "subtitle": "<PERSON><PERSON><PERSON> başarıyla tama<PERSON>landı!", "testButton": "Test <PERSON><PERSON>u"}, "menu": {"dashboard": "<PERSON>", "orders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "categories": "<PERSON><PERSON><PERSON>", "tables": "<PERSON><PERSON><PERSON>", "customers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inventory": "Stok", "kitchen": "Mutfak", "cashier": "<PERSON><PERSON>", "cariler": "<PERSON><PERSON>", "takeaway": "<PERSON><PERSON>", "quickSale": "Hızlı Satış"}, "dashboard": {"title": "<PERSON>", "welcome": "<PERSON><PERSON> Geldiniz", "notifications": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newOrder": "Yeni sipari<PERSON> var!", "orderReady": "Sipariş hazır", "tableRequest": "<PERSON><PERSON>bi", "kitchenAlert": "Mutfak uyarısı", "showAll": "<PERSON><PERSON><PERSON> bildirimleri göster", "noNotifications": "<PERSON>ni bildirim yok", "markAsRead": "<PERSON><PERSON><PERSON> olarak işaretle"}, "weather": {"title": "<PERSON><PERSON>", "temperature": "Sıcaklık", "condition": "Durum", "sunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cloudy": "Bulutlu", "rainy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snowy": "Karlı"}, "clock": {"currentTime": "<PERSON><PERSON> anki saat", "date": "<PERSON><PERSON><PERSON>"}}, "header": {"internet": {"label": "İnternet", "connected": "İnternet bağlantısı aktif", "disconnected": "İnternet bağlantısı yok"}, "backend": {"label": "<PERSON><PERSON><PERSON>", "connected": "Sunucu bağlantısı aktif", "disconnected": "<PERSON><PERSON><PERSON> bağlantısı yok"}, "user": {"menu": "Kullanıcı menüsü", "logout": "Çıkış Yap"}, "language": {"tooltip": "<PERSON><PERSON>", "turkish": "Türkçe", "english": "English"}, "theme": {"tooltip": "<PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>k tema", "dark": "<PERSON><PERSON> tema"}}, "products": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON> y<PERSON> ve kategori işlemleri", "addProduct": "<PERSON><PERSON>", "addCategory": "<PERSON><PERSON>", "editProduct": "<PERSON><PERSON><PERSON><PERSON>", "editCategory": "<PERSON><PERSON><PERSON>", "deleteProduct": "<PERSON><PERSON><PERSON><PERSON>", "deleteCategory": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON>n ara...", "noProducts": "<PERSON><PERSON><PERSON><PERSON> ür<PERSON><PERSON> bulu<PERSON>", "noCategories": "<PERSON><PERSON><PERSON><PERSON> kategori bulu<PERSON><PERSON><PERSON>", "productDetails": "<PERSON><PERSON><PERSON><PERSON>", "categoryDetails": "<PERSON><PERSON><PERSON>", "filters": {"all": "Tümü", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "priceRange": "<PERSON>yat <PERSON>", "featured": "<PERSON><PERSON>", "vegetarian": "Vejetaryen", "vegan": "Vegan", "glutenFree": "G<PERSON><PERSON><PERSON>"}, "form": {"basicInfo": "<PERSON><PERSON>", "pricing": "Fiya<PERSON><PERSON>rma", "inventory": "Stok", "display": "G<PERSON>rü<PERSON><PERSON><PERSON>", "dietary": "Beslenme", "images": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON><PERSON>", "barcode": "Barkod", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "shortDescription": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "basePrice": "Satış Fiyatı", "costPrice": "Maliyet Fiyatı", "profitMargin": "<PERSON><PERSON> (%)", "tax": "Vergi <PERSON>ı", "unit": "<PERSON><PERSON><PERSON>", "trackStock": "Stok Takibi", "allowNegativeStock": "Negatif Stok İzni", "minStockLevel": "Minimum Stok", "maxStockLevel": "<PERSON><PERSON><PERSON><PERSON>ok", "preparationTime": "Hazırlık Süresi (dk)", "calories": "<PERSON><PERSON><PERSON>", "allergens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tags": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktif", "showInMenu": "<PERSON><PERSON><PERSON>", "showInKitchen": "Mutfakta Göster", "allowOnlineOrdering": "Online Sipariş", "displayOrder": "Sıralama", "featured": "<PERSON><PERSON>", "spicy": "Acılı", "vegetarian": "Vejetaryen", "vegan": "Vegan", "glutenFree": "G<PERSON><PERSON><PERSON>", "organic": "Organik", "uploadImages": "<PERSON><PERSON><PERSON>", "dragDropImages": "Resimleri buraya sürükleyin veya tıklayın", "maxImages": "Maksimum 5 resim yükleyebilirsiniz", "imageFormats": "JPG, PNG, GIF, WebP formatları desteklenir"}, "category": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "parentCategory": "Üst Kategori", "color": "Renk", "icon": "İkon", "showInKitchen": "Mutfakta Göster", "preparationTime": "Hazırlık Süresi (dk)", "displayOrder": "Sıralama", "active": "Aktif", "showInMenu": "<PERSON><PERSON><PERSON>", "uploadImage": "<PERSON><PERSON><PERSON>", "noParent": "<PERSON>"}, "units": {"PIECE": "<PERSON><PERSON>", "KG": "Kilogram", "GRAM": "Gram", "LITER": "Litre", "ML": "Mililitre", "PORTION": "Porsiyon", "BOX": "<PERSON><PERSON>", "PACKAGE": "<PERSON><PERSON>"}, "actions": {"save": "<PERSON><PERSON>", "cancel": "İptal", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "duplicate": "Kopyala", "bulkEdit": "<PERSON>lu <PERSON>", "export": "Dışa Aktar", "import": "İçe Aktar"}, "messages": {"productCreated": "<PERSON><PERSON>ün başarıyla oluşturuldu", "productUpdated": "<PERSON><PERSON><PERSON><PERSON> başar<PERSON><PERSON> gü<PERSON>llendi", "productDeleted": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "categoryCreated": "<PERSON><PERSON>i başarıyla oluşturuldu", "categoryUpdated": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "categoryDeleted": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteConfirm": "<PERSON>u işlemi geri alamazsınız. <PERSON><PERSON> misin<PERSON>?", "bulkUpdateSuccess": "Seçili ürünler başarıyla güncellendi"}}, "common": {"save": "<PERSON><PERSON>", "cancel": "İptal", "delete": "Sil", "edit": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON>", "search": "Ara", "filter": "Filtrele", "export": "Dışa Aktar", "import": "İçe Aktar", "print": "Yazdır", "close": "Ka<PERSON><PERSON>", "open": "Aç", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "loading": "Yükleniyor...", "error": "<PERSON><PERSON>", "success": "Başarılı", "warning": "Uyarı", "info": "<PERSON><PERSON><PERSON>"}, "auth": {"login": "<PERSON><PERSON><PERSON>", "logout": "Çıkış Yap", "username": "Kullanıcı Adı", "password": "Şifre", "rememberMe": "<PERSON><PERSON>", "forgotPassword": "Şif<PERSON><PERSON>", "welcome": "<PERSON><PERSON> Geldiniz", "welcomeBack": "<PERSON><PERSON><PERSON>", "loginToAccount": "Hesabınıza giriş yapın", "loginDescription": "Restoran POS sisteminize güvenli bir şekilde giriş ya<PERSON>ın", "enterUsername": "Kullanıcı adınızı girin", "enterPassword": "Şifrenizi girin", "loginButton": "<PERSON><PERSON><PERSON>", "loggingIn": "<PERSON><PERSON><PERSON> yapılıyor...", "loginSuccess": "Giriş başarılı! Yönlendiriliyorsunuz...", "loginError": "<PERSON><PERSON><PERSON> başar<PERSON>s<PERSON><PERSON>", "invalidCredentials": "Kullanıcı adı veya şifre hatalı", "accountLocked": "Hesabınız kilitlenmiş", "serverError": "<PERSON><PERSON><PERSON>, lütfen tekrar deneyin", "networkError": "Bağlantı hatası, internet bağlantınızı kontrol edin", "sessionExpired": "Oturumunuz sona erdi, lütfen tekrar giriş yapın", "hero": {"title": "Modern Restoran Yönetimi", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, masalarınızı ve satışlarınızı tek platformdan yönetin", "testimonial": "Bu sistem sayesinde restoran operasyonlarımız çok daha verimli hale geldi.", "author": "<PERSON><PERSON>", "position": "<PERSON><PERSON>"}}, "orders": {"newOrder": "<PERSON><PERSON>", "orderNumber": "Sipariş No", "table": "<PERSON><PERSON>", "customer": "Müş<PERSON>i", "total": "Toplam", "status": "Durum", "date": "<PERSON><PERSON><PERSON>", "pending": "Bekliyor", "preparing": "Hazırlanıyor", "ready": "Hazır", "delivered": "<PERSON><PERSON><PERSON>", "cancelled": "İptal Edildi"}, "settings": {"general": "<PERSON><PERSON>", "language": "Dil", "theme": "<PERSON><PERSON>", "currency": "Para Birimi", "tax": "<PERSON><PERSON><PERSON>", "printer": "Yazıcı", "backup": "<PERSON><PERSON><PERSON><PERSON>"}}
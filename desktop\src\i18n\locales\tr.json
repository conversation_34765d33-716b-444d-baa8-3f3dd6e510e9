{"app": {"title": "Restoran POS Sistemi", "subtitle": "<PERSON><PERSON><PERSON> başarıyla tama<PERSON>landı!", "testButton": "Test <PERSON><PERSON>u"}, "menu": {"dashboard": "<PERSON>", "orders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "categories": "<PERSON><PERSON><PERSON>", "tables": "<PERSON><PERSON><PERSON>", "customers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reports": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inventory": "Stok", "kitchen": "Mutfak", "cashier": "<PERSON><PERSON>", "cariler": "<PERSON><PERSON>", "takeaway": "<PERSON><PERSON>", "quickSale": "Hızlı Satış"}, "dashboard": {"title": "<PERSON>", "welcome": "<PERSON><PERSON> Geldiniz", "notifications": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newOrder": "Yeni sipari<PERSON> var!", "orderReady": "Sipariş hazır", "tableRequest": "<PERSON><PERSON>bi", "kitchenAlert": "Mutfak uyarısı", "showAll": "<PERSON><PERSON><PERSON> bildirimleri göster", "noNotifications": "<PERSON>ni bildirim yok", "markAsRead": "<PERSON><PERSON><PERSON> olarak işaretle"}, "weather": {"title": "<PERSON><PERSON>", "temperature": "Sıcaklık", "condition": "Durum", "sunny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cloudy": "Bulutlu", "rainy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "snowy": "Karlı"}, "clock": {"currentTime": "<PERSON><PERSON> anki saat", "date": "<PERSON><PERSON><PERSON>"}}, "header": {"internet": {"label": "İnternet", "connected": "İnternet bağlantısı aktif", "disconnected": "İnternet bağlantısı yok"}, "backend": {"label": "<PERSON><PERSON><PERSON>", "connected": "Sunucu bağlantısı aktif", "disconnected": "<PERSON><PERSON><PERSON> bağlantısı yok"}, "user": {"menu": "Kullanıcı menüsü", "logout": "Çıkış Yap"}, "language": {"tooltip": "<PERSON><PERSON>", "turkish": "Türkçe", "english": "English"}, "theme": {"tooltip": "<PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON>k tema", "dark": "<PERSON><PERSON> tema"}}, "products": {"name": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "stock": "Stok", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "image": "Resim", "barcode": "Barkod", "unit": "<PERSON><PERSON><PERSON>"}, "common": {"save": "<PERSON><PERSON>", "cancel": "İptal", "delete": "Sil", "edit": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON>", "search": "Ara", "filter": "Filtrele", "export": "Dışa Aktar", "import": "İçe Aktar", "print": "Yazdır", "close": "Ka<PERSON><PERSON>", "open": "Aç", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "loading": "Yükleniyor...", "error": "<PERSON><PERSON>", "success": "Başarılı", "warning": "Uyarı", "info": "<PERSON><PERSON><PERSON>"}, "auth": {"login": "<PERSON><PERSON><PERSON>", "logout": "Çıkış Yap", "username": "Kullanıcı Adı", "password": "Şifre", "rememberMe": "<PERSON><PERSON>", "forgotPassword": "Şif<PERSON><PERSON>", "welcome": "<PERSON><PERSON> Geldiniz", "welcomeBack": "<PERSON><PERSON><PERSON>", "loginToAccount": "Hesabınıza giriş yapın", "loginDescription": "Restoran POS sisteminize güvenli bir şekilde giriş ya<PERSON>ın", "enterUsername": "Kullanıcı adınızı girin", "enterPassword": "Şifrenizi girin", "loginButton": "<PERSON><PERSON><PERSON>", "loggingIn": "<PERSON><PERSON><PERSON> yapılıyor...", "loginSuccess": "Giriş başarılı! Yönlendiriliyorsunuz...", "loginError": "<PERSON><PERSON><PERSON> başar<PERSON>s<PERSON><PERSON>", "invalidCredentials": "Kullanıcı adı veya şifre hatalı", "accountLocked": "Hesabınız kilitlenmiş", "serverError": "<PERSON><PERSON><PERSON>, lütfen tekrar deneyin", "networkError": "Bağlantı hatası, internet bağlantınızı kontrol edin", "sessionExpired": "Oturumunuz sona erdi, lütfen tekrar giriş yapın", "hero": {"title": "Modern Restoran Yönetimi", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, masalarınızı ve satışlarınızı tek platformdan yönetin", "testimonial": "Bu sistem sayesinde restoran operasyonlarımız çok daha verimli hale geldi.", "author": "<PERSON><PERSON>", "position": "<PERSON><PERSON>"}}, "orders": {"newOrder": "<PERSON><PERSON>", "orderNumber": "Sipariş No", "table": "<PERSON><PERSON>", "customer": "Müş<PERSON>i", "total": "Toplam", "status": "Durum", "date": "<PERSON><PERSON><PERSON>", "pending": "Bekliyor", "preparing": "Hazırlanıyor", "ready": "Hazır", "delivered": "<PERSON><PERSON><PERSON>", "cancelled": "İptal Edildi"}, "settings": {"general": "<PERSON><PERSON>", "language": "Dil", "theme": "<PERSON><PERSON>", "currency": "Para Birimi", "tax": "<PERSON><PERSON><PERSON>", "printer": "Yazıcı", "backup": "<PERSON><PERSON><PERSON><PERSON>"}}
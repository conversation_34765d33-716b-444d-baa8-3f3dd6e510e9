import React from 'react'
import { <PERSON>, Typography, Grid, Card, CardContent } from '@mui/material'
import {
  Inventory,
  People,
  Restaurant,
  Assessment,
  TableBar,
  Category,
  LocalShipping,
  FlashOn
} from '@mui/icons-material'
import { useTranslation } from 'react-i18next'
import { NotificationPanel, WeatherWidget, ClockWidget, DashboardHeader } from '../../components/dashboard'

export const DashboardPage: React.FC = () => {
  const { t } = useTranslation()

  // Dashboard butonları
  const dashboardButtons = [
    {
      title: t('menu.products'),
      icon: <Category sx={{ fontSize: 40 }} />,
      color: '#1976d2',
      path: '/products'
    },
    {
      title: t('menu.inventory'),
      icon: <Inventory sx={{ fontSize: 40 }} />,
      color: '#388e3c',
      path: '/inventory'
    },
    {
      title: t('menu.cariler'),
      icon: <People sx={{ fontSize: 40 }} />,
      color: '#f57c00',
      path: '/customers'
    },
    {
      title: t('menu.kitchen'),
      icon: <Restaurant sx={{ fontSize: 40 }} />,
      color: '#d32f2f',
      path: '/kitchen'
    },
    {
      title: t('menu.reports'),
      icon: <Assessment sx={{ fontSize: 40 }} />,
      color: '#7b1fa2',
      path: '/reports'
    },
    {
      title: t('menu.tables'),
      icon: <TableBar sx={{ fontSize: 40 }} />,
      color: '#455a64',
      path: '/tables'
    },
    {
      title: t('menu.takeaway'),
      icon: <LocalShipping sx={{ fontSize: 40 }} />,
      color: '#00796b',
      path: '/takeaway'
    },
    {
      title: t('menu.quickSale'),
      icon: <FlashOn sx={{ fontSize: 40 }} />,
      color: '#ff5722',
      path: '/quick-sale'
    }
  ]

  return (
    <Box sx={{
      height: '100vh',
      width: '100vw',
      p: { xs: 1, sm: 2, md: 3 },
      backgroundColor: 'background.default',
      overflow: 'auto',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <DashboardHeader />



      <Box sx={{ flex: 1, minHeight: 0, overflow: 'auto' }}>
        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ height: '100%' }}>
          {/* Sol Panel - Bildirimler - Daha Dar */}
          <Grid size={{ xs: 12, md: 5, lg: 4 }}>
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: { xs: 1.5, sm: 2 },
              height: '100%'
            }}>
              {/* Widget'lar Yan Yana */}
              <Box sx={{
                display: 'flex',
                gap: { xs: 1.5, sm: 2 },
                flexDirection: { xs: 'column', sm: 'row' },
                flexShrink: 0
              }}>
                <Box sx={{
                  flex: 1,
                  height: { xs: '80px', sm: '100px' },
                  minHeight: '80px'
                }}>
                  <ClockWidget />
                </Box>
                <Box sx={{
                  flex: 1,
                  height: { xs: '80px', sm: '100px' },
                  minHeight: '80px'
                }}>
                  <WeatherWidget />
                </Box>
              </Box>

              {/* Bildirimler Paneli - Scrollable */}
              <Box sx={{
                flex: 1,
                minHeight: 0,
                '& > *': {
                  height: '100%'
                }
              }}>
                <NotificationPanel />
              </Box>
            </Box>
          </Grid>

          {/* Sağ Panel - Ana İçerik - Çok Geniş */}
          <Grid size={{ xs: 12, md: 7, lg: 8 }}>
            <Box sx={{
              height: '100%',
              backgroundColor: 'background.paper',
              borderRadius: { xs: 1, sm: 2 },
              p: { xs: 2, sm: 3 },
              overflow: 'auto',
              // Custom scrollbar styling
              '&::-webkit-scrollbar': {
                width: '6px'
              },
              '&::-webkit-scrollbar-track': {
                background: 'transparent'
              },
              '&::-webkit-scrollbar-thumb': {
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '3px',
                '&:hover': {
                  background: 'rgba(0, 0, 0, 0.3)'
                }
              }
            }}>
              {/* Dashboard Butonları - Modern Design */}
              <Grid container spacing={4} sx={{
                minHeight: 'fit-content',
                pb: 2 // Alt padding ekle
              }}>
                {dashboardButtons.map((button, index) => (
                  <Grid size={{ xs: 12, sm: 6, lg: 4 }} key={index}>
                    <Card
                      sx={{
                        height: '160px',
                        cursor: 'pointer',
                        borderRadius: 3,
                        border: 'none',
                        background: `linear-gradient(135deg, ${button.color}15 0%, ${button.color}08 100%)`,
                        backdropFilter: 'blur(10px)',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        position: 'relative',
                        overflow: 'hidden',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: `linear-gradient(135deg, ${button.color}20 0%, transparent 50%)`,
                          opacity: 0,
                          transition: 'opacity 0.3s ease'
                        },
                        '&:hover': {
                          transform: 'translateY(-8px) scale(1.02)',
                          boxShadow: `0 20px 40px ${button.color}25, 0 0 0 1px ${button.color}20`,
                          '&::before': {
                            opacity: 1
                          }
                        },
                        '&:active': {
                          transform: 'translateY(-4px) scale(1.01)'
                        }
                      }}
                      onClick={() => {
                        // TODO: Navigate to button.path
                        console.log('Navigate to:', button.path)
                      }}
                    >
                      <CardContent sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        textAlign: 'center',
                        p: 3,
                        position: 'relative',
                        zIndex: 1
                      }}>
                        {/* Icon Container */}
                        <Box sx={{
                          width: 64,
                          height: 64,
                          borderRadius: '50%',
                          background: `linear-gradient(135deg, ${button.color} 0%, ${button.color}CC 100%)`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mb: 2,
                          boxShadow: `0 8px 24px ${button.color}40`,
                          transition: 'all 0.3s ease'
                        }}>
                          {React.cloneElement(button.icon, {
                            sx: {
                              fontSize: 32,
                              color: 'white',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }
                          })}
                        </Box>

                        {/* Title */}
                        <Typography
                          variant="h6"
                          fontWeight="600"
                          sx={{
                            fontSize: { xs: '1rem', sm: '1.1rem' },
                            color: 'text.primary',
                            letterSpacing: '0.5px',
                            lineHeight: 1.2
                          }}
                        >
                          {button.title}
                        </Typography>

                        {/* Decorative Element */}
                        <Box sx={{
                          position: 'absolute',
                          top: 16,
                          right: 16,
                          width: 6,
                          height: 6,
                          borderRadius: '50%',
                          backgroundColor: button.color,
                          opacity: 0.6
                        }} />
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}

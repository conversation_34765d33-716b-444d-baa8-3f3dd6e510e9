import React, { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Typography,
  Paper,
  IconButton,
  Grid,
  Alert,
  LinearProgress,
  Chip
} from '@mui/material'
import {
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  Image as ImageIcon
} from '@mui/icons-material'

interface FileUploadProps {
  onFilesChange: (files: File[]) => void
  maxFiles?: number
  maxFileSize?: number // in bytes
  acceptedTypes?: string[]
  helperText?: string
  disabled?: boolean
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFilesChange,
  maxFiles = 5,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  helperText,
  disabled = false
}) => {
  const { t } = useTranslation()
  const [files, setFiles] = useState<File[]>([])
  const [dragActive, setDragActive] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [uploading, setUploading] = useState(false)

  // Handle file validation
  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `Geçersiz dosya türü: ${file.type}`
    }
    if (file.size > maxFileSize) {
      return `Dosya boyutu çok büyük: ${(file.size / 1024 / 1024).toFixed(2)}MB`
    }
    return null
  }

  // Handle files
  const handleFiles = useCallback((newFiles: FileList | File[]) => {
    setError(null)
    const fileArray = Array.from(newFiles)
    const validFiles: File[] = []
    const errors: string[] = []

    // Check total file count
    if (files.length + fileArray.length > maxFiles) {
      setError(`Maksimum ${maxFiles} dosya yükleyebilirsiniz`)
      return
    }

    // Validate each file
    fileArray.forEach(file => {
      const validationError = validateFile(file)
      if (validationError) {
        errors.push(`${file.name}: ${validationError}`)
      } else {
        validFiles.push(file)
      }
    })

    if (errors.length > 0) {
      setError(errors.join(', '))
      return
    }

    // Update files
    const updatedFiles = [...files, ...validFiles]
    setFiles(updatedFiles)
    onFilesChange(updatedFiles)
  }, [files, maxFiles, maxFileSize, acceptedTypes, onFilesChange])

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  // Handle drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (disabled) return

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }, [disabled, handleFiles])

  // Handle file input change
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    if (disabled) return

    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files)
    }
  }, [disabled, handleFiles])

  // Remove file
  const removeFile = (index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index)
    setFiles(updatedFiles)
    onFilesChange(updatedFiles)
  }

  // Create preview URL
  const createPreviewUrl = (file: File): string => {
    return URL.createObjectURL(file)
  }

  return (
    <Box>
      {/* Upload Area */}
      <Paper
        sx={{
          border: 2,
          borderStyle: 'dashed',
          borderColor: dragActive ? 'primary.main' : 'grey.300',
          bgcolor: dragActive ? 'action.hover' : 'background.paper',
          p: 3,
          textAlign: 'center',
          cursor: disabled ? 'not-allowed' : 'pointer',
          opacity: disabled ? 0.6 : 1,
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            borderColor: disabled ? 'grey.300' : 'primary.main',
            bgcolor: disabled ? 'background.paper' : 'action.hover'
          }
        }}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => {
          if (!disabled) {
            document.getElementById('file-upload-input')?.click()
          }
        }}
      >
        <input
          id="file-upload-input"
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleChange}
          style={{ display: 'none' }}
          disabled={disabled}
        />
        
        <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {t('products.form.dragDropImages')}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {helperText || t('products.form.imageFormats')}
        </Typography>
        <Typography variant="caption" display="block" sx={{ mt: 1 }}>
          Maksimum {maxFiles} dosya, her biri {(maxFileSize / 1024 / 1024).toFixed(0)}MB'dan küçük
        </Typography>
      </Paper>

      {/* Error Message */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {/* Upload Progress */}
      {uploading && (
        <Box sx={{ mt: 2 }}>
          <LinearProgress />
          <Typography variant="caption" color="text.secondary">
            Dosyalar yükleniyor...
          </Typography>
        </Box>
      )}

      {/* File List */}
      {files.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Seçili Dosyalar ({files.length}/{maxFiles})
          </Typography>
          <Grid container spacing={2}>
            {files.map((file, index) => (
              <Grid item xs={6} sm={4} md={3} key={index}>
                <Paper
                  sx={{
                    p: 1,
                    position: 'relative',
                    aspectRatio: '1',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {/* Remove Button */}
                  <IconButton
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: 4,
                      right: 4,
                      bgcolor: 'rgba(0,0,0,0.5)',
                      color: 'white',
                      '&:hover': {
                        bgcolor: 'rgba(0,0,0,0.7)'
                      }
                    }}
                    onClick={() => removeFile(index)}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>

                  {/* Preview */}
                  {file.type.startsWith('image/') ? (
                    <Box
                      component="img"
                      src={createPreviewUrl(file)}
                      alt={file.name}
                      sx={{
                        width: '100%',
                        height: '80%',
                        objectFit: 'cover',
                        borderRadius: 1
                      }}
                    />
                  ) : (
                    <ImageIcon sx={{ fontSize: 40, color: 'text.secondary' }} />
                  )}

                  {/* File Info */}
                  <Typography
                    variant="caption"
                    sx={{
                      mt: 0.5,
                      textAlign: 'center',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      width: '100%'
                    }}
                  >
                    {file.name}
                  </Typography>
                  <Chip
                    label={`${(file.size / 1024).toFixed(0)}KB`}
                    size="small"
                    variant="outlined"
                    sx={{ mt: 0.5 }}
                  />
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Box>
  )
}

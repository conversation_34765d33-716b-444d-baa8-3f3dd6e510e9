import { create } from 'zustand'
import { 
  Category, 
  CategoryQuery, 
  CreateCategoryForm, 
  UpdateCategoryForm,
  ReorderCategories,
  CategoryListResponse 
} from 'shared'
import { categoryService } from '../services/productService'

interface CategoryState {
  // State
  categories: Category[]
  selectedCategory: Category | null
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    totalCount: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  } | null
  hierarchical: boolean

  // Actions
  fetchCategories: (query?: CategoryQuery) => Promise<void>
  fetchCategory: (id: string) => Promise<void>
  createCategory: (data: CreateCategoryForm) => Promise<Category>
  updateCategory: (id: string, data: UpdateCategoryForm) => Promise<Category>
  deleteCategory: (id: string) => Promise<void>
  reorderCategories: (data: ReorderCategories) => Promise<void>
  setSelectedCategory: (category: Category | null) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useCategoryStore = create<CategoryState>((set, get) => ({
  // Initial State
  categories: [],
  selectedCategory: null,
  loading: false,
  error: null,
  pagination: null,
  hierarchical: false,

  // Actions
  fetchCategories: async (query?: CategoryQuery) => {
    set({ loading: true, error: null })
    try {
      const response: CategoryListResponse = await categoryService.getCategories(query)
      set({ 
        categories: response.categories,
        pagination: response.pagination || null,
        hierarchical: response.hierarchical || false,
        loading: false 
      })
    } catch (error: any) {
      set({ 
        error: error.response?.data?.error || 'Kategoriler yüklenirken hata oluştu',
        loading: false 
      })
    }
  },

  fetchCategory: async (id: string) => {
    set({ loading: true, error: null })
    try {
      const category = await categoryService.getCategory(id)
      set({ 
        selectedCategory: category,
        loading: false 
      })
    } catch (error: any) {
      set({ 
        error: error.response?.data?.error || 'Kategori yüklenirken hata oluştu',
        loading: false 
      })
    }
  },

  createCategory: async (data: CreateCategoryForm) => {
    set({ loading: true, error: null })
    try {
      const category = await categoryService.createCategory(data)
      
      // Add to categories list
      set(state => ({ 
        categories: [category, ...state.categories],
        loading: false 
      }))
      
      return category
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Kategori oluşturulurken hata oluştu'
      set({ 
        error: errorMessage,
        loading: false 
      })
      throw new Error(errorMessage)
    }
  },

  updateCategory: async (id: string, data: UpdateCategoryForm) => {
    set({ loading: true, error: null })
    try {
      const category = await categoryService.updateCategory(id, data)
      
      // Update in categories list
      set(state => ({ 
        categories: state.categories.map(c => c.id === id ? category : c),
        selectedCategory: state.selectedCategory?.id === id ? category : state.selectedCategory,
        loading: false 
      }))
      
      return category
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Kategori güncellenirken hata oluştu'
      set({ 
        error: errorMessage,
        loading: false 
      })
      throw new Error(errorMessage)
    }
  },

  deleteCategory: async (id: string) => {
    set({ loading: true, error: null })
    try {
      await categoryService.deleteCategory(id)
      
      // Remove from categories list
      set(state => ({ 
        categories: state.categories.filter(c => c.id !== id),
        selectedCategory: state.selectedCategory?.id === id ? null : state.selectedCategory,
        loading: false 
      }))
    } catch (error: any) {
      set({ 
        error: error.response?.data?.error || 'Kategori silinirken hata oluştu',
        loading: false 
      })
    }
  },

  reorderCategories: async (data: ReorderCategories) => {
    set({ loading: true, error: null })
    try {
      await categoryService.reorderCategories(data)
      
      // Refresh categories list
      const { fetchCategories } = get()
      await fetchCategories()
    } catch (error: any) {
      set({ 
        error: error.response?.data?.error || 'Sıralama güncellenirken hata oluştu',
        loading: false 
      })
    }
  },

  setSelectedCategory: (category: Category | null) => {
    set({ selectedCategory: category })
  },

  clearError: () => {
    set({ error: null })
  },

  setLoading: (loading: boolean) => {
    set({ loading })
  }
}))

import { Request, Response } from 'express'
import { authService } from '../services/authService'
import prisma from '../lib/prisma'
import bcrypt from 'bcryptjs'

/**
 * Kullanıcı girişi
 */
export const login = async (req: Request, res: Response) => {
  try {
    const { username, password } = req.body

    // Input validation
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        error: 'Kullanıcı adı ve şifre gerekli'
      })
    }

    const result = await authService.login({ username, password })

    if (!result.success) {
      return res.status(401).json(result)
    }

    // Basit response
    res.json(result)
  } catch (error) {
    console.error('Login controller error:', error)
    res.status(500).json({
      success: false,
      error: 'Sunucu hatası'
    })
  }
}

// Refresh token artık gerekli değil - basit token sistemi

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> çıkışı
 */
export const logout = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId

    if (userId) {
      await authService.logout(userId)
    }

    res.json({
      success: true,
      message: 'Çıkış başarılı'
    })
  } catch (error) {
    console.error('Logout controller error:', error)
    res.status(500).json({
      success: false,
      error: 'Sunucu hatası'
    })
  }
}

/**
 * Kullanıcı profili
 */
export const getProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'Kullanıcı kimliği bulunamadı'
      })
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        active: true,
        createdAt: true,
        lastLoginAt: true,
        company: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        branch: {
          select: {
            id: true,
            name: true,
            code: true
          }
        }
      }
    })

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Kullanıcı bulunamadı'
      })
    }

    res.json({
      success: true,
      data: user
    })
  } catch (error) {
    console.error('Get profile controller error:', error)
    res.status(500).json({
      success: false,
      error: 'Sunucu hatası'
    })
  }
}

/**
 * Şifre değiştirme
 */
export const changePassword = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.userId
    const { currentPassword, newPassword } = req.body

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: 'Mevcut şifre ve yeni şifre gerekli'
      })
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        error: 'Yeni şifre en az 6 karakter olmalı'
      })
    }

    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Kullanıcı bulunamadı'
      })
    }

    // Mevcut şifre kontrolü
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password)

    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        error: 'Mevcut şifre hatalı'
      })
    }

    // Yeni şifreyi hash'le
    const hashedNewPassword = await bcrypt.hash(newPassword, 10)

    // Şifreyi güncelle
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedNewPassword
      }
    })

    res.json({
      success: true,
      message: 'Şifre başarıyla değiştirildi. Lütfen tekrar giriş yapın.'
    })
  } catch (error) {
    console.error('Change password controller error:', error)
    res.status(500).json({
      success: false,
      error: 'Sunucu hatası'
    })
  }
}

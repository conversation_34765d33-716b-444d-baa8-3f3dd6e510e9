import { z } from 'zod'

// Product Unit enum validation
export const ProductUnitSchema = z.enum([
  'PIECE',
  'KG', 
  'GRAM',
  'LITER',
  'ML',
  'PORTION',
  'BOX',
  'PACKAGE'
])

// Product Type enum validation
export const ProductTypeSchema = z.enum([
  'SIMPLE',
  'VARIANT',
  'COMBO',
  'RECIPE'
])

// Base Product validation schema
export const BaseProductSchema = z.object({
  categoryId: z.string().min(1, 'Kategori seçimi zorunludur'),
  code: z.string()
    .min(1, 'Ürün kodu zorunludur')
    .max(50, 'Ürün kodu maksimum 50 karakter olabilir')
    .regex(/^[A-Za-z0-9_-]+$/, '<PERSON>rün kodu sadece harf, rakam, tire ve alt çizgi içerebilir'),
  barcode: z.string()
    .max(50, 'Barkod maksimum 50 karakter olabilir')
    .optional()
    .nullable(),
  name: z.string()
    .min(1, '<PERSON>r<PERSON>n adı zorunludur')
    .max(200, '<PERSON>r<PERSON>n adı maksimum 200 karakter olabilir'),
  description: z.string()
    .max(1000, 'Açıklama maksimum 1000 karakter olabilir')
    .optional()
    .nullable(),
  shortDescription: z.string()
    .max(200, 'Kısa açıklama maksimum 200 karakter olabilir')
    .optional()
    .nullable(),
  basePrice: z.number()
    .min(0, 'Fiyat 0 veya pozitif olmalıdır')
    .max(999999.99, 'Fiyat çok yüksek'),
  taxId: z.string().min(1, 'Vergi oranı seçimi zorunludur'),
  costPrice: z.number()
    .min(0, 'Maliyet fiyatı 0 veya pozitif olmalıdır')
    .max(999999.99, 'Maliyet fiyatı çok yüksek')
    .optional()
    .nullable(),
  profitMargin: z.number()
    .min(0, 'Kar marjı 0 veya pozitif olmalıdır')
    .max(100, 'Kar marjı maksimum %100 olabilir')
    .optional()
    .nullable(),
  unit: ProductUnitSchema,
  type: ProductTypeSchema.default('SIMPLE'),
  trackStock: z.boolean().default(true),
  allowNegativeStock: z.boolean().default(false),
  minStockLevel: z.number()
    .min(0, 'Minimum stok seviyesi 0 veya pozitif olmalıdır')
    .optional()
    .nullable(),
  maxStockLevel: z.number()
    .min(0, 'Maksimum stok seviyesi 0 veya pozitif olmalıdır')
    .optional()
    .nullable(),
  preparationTime: z.number()
    .min(0, 'Hazırlık süresi 0 veya pozitif olmalıdır')
    .max(1440, 'Hazırlık süresi maksimum 24 saat (1440 dakika) olabilir')
    .optional()
    .nullable(),
  calories: z.number()
    .min(0, 'Kalori 0 veya pozitif olmalıdır')
    .max(10000, 'Kalori çok yüksek')
    .optional()
    .nullable(),
  allergens: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  active: z.boolean().default(true),
  showInMenu: z.boolean().default(true),
  showInKitchen: z.boolean().default(true),
  allowOnlineOrdering: z.boolean().default(true),
  displayOrder: z.number().min(0).default(0),
  featured: z.boolean().default(false),
  spicy: z.boolean().default(false),
  vegetarian: z.boolean().default(false),
  vegan: z.boolean().default(false),
  glutenFree: z.boolean().default(false),
  organic: z.boolean().default(false)
})

// Product Create Schema
export const CreateProductSchema = BaseProductSchema.extend({
  // Resim dosyaları opsiyonel (multer tarafından handle edilecek)
  image: z.string().optional().nullable(),
  images: z.array(z.string()).default([])
})

// Product Update Schema - tüm alanlar opsiyonel
export const UpdateProductSchema = BaseProductSchema.partial().extend({
  id: z.string().min(1, 'Ürün ID zorunludur'),
  image: z.string().optional().nullable(),
  images: z.array(z.string()).optional()
})

// Product Query Schema (filtering, sorting, pagination)
export const ProductQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  categoryId: z.string().optional(),
  active: z.coerce.boolean().optional(),
  showInMenu: z.coerce.boolean().optional(),
  type: ProductTypeSchema.optional(),
  minPrice: z.coerce.number().min(0).optional(),
  maxPrice: z.coerce.number().min(0).optional(),
  sortBy: z.enum(['name', 'basePrice', 'createdAt', 'updatedAt', 'displayOrder']).default('displayOrder'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  featured: z.coerce.boolean().optional(),
  vegetarian: z.coerce.boolean().optional(),
  vegan: z.coerce.boolean().optional(),
  glutenFree: z.coerce.boolean().optional()
})

// Product ID validation
export const ProductIdSchema = z.object({
  id: z.string().min(1, 'Ürün ID zorunludur')
})

// Bulk operations schema
export const BulkProductUpdateSchema = z.object({
  productIds: z.array(z.string().min(1)).min(1, 'En az bir ürün seçilmelidir'),
  updates: z.object({
    active: z.boolean().optional(),
    showInMenu: z.boolean().optional(),
    showInKitchen: z.boolean().optional(),
    categoryId: z.string().optional(),
    displayOrder: z.number().min(0).optional()
  }).refine(data => Object.keys(data).length > 0, {
    message: 'En az bir güncelleme alanı belirtilmelidir'
  })
})

// Product duplicate schema
export const DuplicateProductSchema = z.object({
  id: z.string().min(1, 'Kaynak ürün ID zorunludur'),
  newCode: z.string()
    .min(1, 'Yeni ürün kodu zorunludur')
    .max(50, 'Ürün kodu maksimum 50 karakter olabilir')
    .regex(/^[A-Za-z0-9_-]+$/, 'Ürün kodu sadece harf, rakam, tire ve alt çizgi içerebilir'),
  newName: z.string()
    .min(1, 'Yeni ürün adı zorunludur')
    .max(200, 'Ürün adı maksimum 200 karakter olabilir')
})

// Type exports
export type CreateProductInput = z.infer<typeof CreateProductSchema>
export type UpdateProductInput = z.infer<typeof UpdateProductSchema>
export type ProductQueryInput = z.infer<typeof ProductQuerySchema>
export type ProductIdInput = z.infer<typeof ProductIdSchema>
export type BulkProductUpdateInput = z.infer<typeof BulkProductUpdateSchema>
export type DuplicateProductInput = z.infer<typeof DuplicateProductSchema>
export type ProductUnit = z.infer<typeof ProductUnitSchema>
export type ProductType = z.infer<typeof ProductTypeSchema>

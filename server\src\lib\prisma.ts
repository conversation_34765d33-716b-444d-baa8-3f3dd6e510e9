import { PrismaClient } from '@prisma/client'

// Global Prisma instance
declare global {
  var __prisma: PrismaClient | undefined
}

// Prisma client instance
export const prisma = globalThis.__prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
})

// Development ortamında global instance kullan (hot reload için)
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma
}

// Graceful shutdown
process.on('beforeExit', async () => {
  await prisma.$disconnect()
})

export default prisma

import { Router } from 'express'
import {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  reorderCategories
} from '../controllers/categoryController'
import { authenticateToken } from '../middlewares/authMiddleware'
import { validate, validateQuery, validateParams } from '../middlewares/validationMiddleware'
import { uploadSingle, handleUploadError } from '../middlewares/uploadMiddleware'
import {
  CreateCategorySchema,
  UpdateCategorySchema,
  CategoryQuerySchema,
  CategoryIdSchema,
  ReorderCategoriesSchema
} from '../validators/categoryValidators'

const router = Router()

// Tüm routes için authentication gerekli
router.use(authenticateToken)

/**
 * GET /api/categories
 * Tüm kategorileri listele (pagination, filtering, sorting, hierarchical)
 */
router.get('/',
  validateQuery(CategoryQuerySchema),
  getCategories
)

/**
 * GET /api/categories/:id
 * Tek kategori detayı getir
 */
router.get('/:id',
  validateParams(CategoryIdSchema),
  getCategory
)

/**
 * POST /api/categories
 * Yeni kategori oluştur (resim yükleme ile)
 */
router.post('/',
  uploadSingle('image'), // Tek resim
  handleUploadError,
  validate(CreateCategorySchema),
  createCategory
)

/**
 * PUT /api/categories/:id
 * Kategori güncelle (resim yükleme ile)
 */
router.put('/:id',
  validateParams(CategoryIdSchema),
  uploadSingle('image'), // Tek resim
  handleUploadError,
  validate(UpdateCategorySchema),
  updateCategory
)

/**
 * DELETE /api/categories/:id
 * Kategori sil (soft delete)
 */
router.delete('/:id',
  validateParams(CategoryIdSchema),
  deleteCategory
)

/**
 * PATCH /api/categories/reorder
 * Kategori sıralama güncelle
 */
router.patch('/reorder',
  validate(ReorderCategoriesSchema),
  reorderCategories
)

export default router

import { z } from 'zod'

// Base Category validation schema
export const BaseCategorySchema = z.object({
  parentId: z.string()
    .optional()
    .nullable()
    .refine((val) => {
      // Parent ID varsa boş string olmamalı
      if (val === '') return false
      return true
    }, {
      message: 'Üst kategori ID geçersiz'
    }),
  name: z.string()
    .min(1, 'Kategori adı zorunludur')
    .max(100, 'Kategori adı maksimum 100 karakter olabilir')
    .trim(),
  description: z.string()
    .max(500, 'Açıklama maksimum 500 karakter olabilir')
    .optional()
    .nullable(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, 'Geçerli bir hex renk kodu giriniz (#RRGGBB)')
    .optional()
    .nullable(),
  icon: z.string()
    .max(50, '<PERSON><PERSON> adı maksimum 50 karakter olabilir')
    .optional()
    .nullable(),
  showInKitchen: z.boolean().default(true),
  preparationTime: z.number()
    .min(0, 'Hazırlık süresi 0 veya pozitif olmalıdır')
    .max(1440, 'Hazırlık süresi maksimum 24 saat (1440 dakika) olabilir')
    .optional()
    .nullable(),
  displayOrder: z.number()
    .min(0, 'Sıralama 0 veya pozitif olmalıdır')
    .default(0),
  active: z.boolean().default(true),
  showInMenu: z.boolean().default(true)
})

// Category Create Schema
export const CreateCategorySchema = BaseCategorySchema.extend({
  // Resim dosyası opsiyonel (multer tarafından handle edilecek)
  image: z.string().optional().nullable()
}).refine((data) => {
  // Eğer parentId varsa, kendi kendisine parent olamaz kontrolü
  // Bu kontrol backend'de daha detaylı yapılacak
  return true
}, {
  message: 'Kategori kendi kendisine üst kategori olamaz'
})

// Category Update Schema - tüm alanlar opsiyonel
export const UpdateCategorySchema = BaseCategorySchema.partial().extend({
  id: z.string().min(1, 'Kategori ID zorunludur'),
  image: z.string().optional().nullable()
}).refine((data) => {
  // ID ile parentId aynı olamaz
  if (data.id && data.parentId && data.id === data.parentId) {
    return false
  }
  return true
}, {
  message: 'Kategori kendi kendisine üst kategori olamaz'
})

// Category Query Schema (filtering, sorting, pagination)
export const CategoryQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  parentId: z.string().optional().nullable(),
  active: z.coerce.boolean().optional(),
  showInMenu: z.coerce.boolean().optional(),
  showInKitchen: z.coerce.boolean().optional(),
  sortBy: z.enum(['name', 'displayOrder', 'createdAt', 'updatedAt']).default('displayOrder'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  includeProducts: z.coerce.boolean().default(false), // Ürünleri de getir
  includeChildren: z.coerce.boolean().default(false), // Alt kategorileri de getir
  hierarchical: z.coerce.boolean().default(false) // Hiyerarşik yapıda getir
})

// Category ID validation
export const CategoryIdSchema = z.object({
  id: z.string().min(1, 'Kategori ID zorunludur')
})

// Category hierarchy validation
export const CategoryHierarchySchema = z.object({
  id: z.string().min(1, 'Kategori ID zorunludur'),
  includeProducts: z.coerce.boolean().default(false),
  maxDepth: z.coerce.number().min(1).max(10).default(5) // Maksimum derinlik
})

// Bulk operations schema
export const BulkCategoryUpdateSchema = z.object({
  categoryIds: z.array(z.string().min(1)).min(1, 'En az bir kategori seçilmelidir'),
  updates: z.object({
    active: z.boolean().optional(),
    showInMenu: z.boolean().optional(),
    showInKitchen: z.boolean().optional(),
    parentId: z.string().optional().nullable(),
    displayOrder: z.number().min(0).optional(),
    color: z.string()
      .regex(/^#[0-9A-Fa-f]{6}$/, 'Geçerli bir hex renk kodu giriniz (#RRGGBB)')
      .optional()
      .nullable()
  }).refine(data => Object.keys(data).length > 0, {
    message: 'En az bir güncelleme alanı belirtilmelidir'
  })
})

// Category move schema (parent değiştirme)
export const MoveCategorySchema = z.object({
  id: z.string().min(1, 'Kategori ID zorunludur'),
  newParentId: z.string().optional().nullable(),
  newDisplayOrder: z.number().min(0).optional()
}).refine((data) => {
  // ID ile newParentId aynı olamaz
  if (data.id && data.newParentId && data.id === data.newParentId) {
    return false
  }
  return true
}, {
  message: 'Kategori kendi kendisine üst kategori olamaz'
})

// Category duplicate schema
export const DuplicateCategorySchema = z.object({
  id: z.string().min(1, 'Kaynak kategori ID zorunludur'),
  newName: z.string()
    .min(1, 'Yeni kategori adı zorunludur')
    .max(100, 'Kategori adı maksimum 100 karakter olabilir'),
  includeProducts: z.boolean().default(false), // Ürünleri de kopyala
  includeChildren: z.boolean().default(false) // Alt kategorileri de kopyala
})

// Category reorder schema
export const ReorderCategoriesSchema = z.object({
  categories: z.array(z.object({
    id: z.string().min(1, 'Kategori ID zorunludur'),
    displayOrder: z.number().min(0, 'Sıralama 0 veya pozitif olmalıdır')
  })).min(1, 'En az bir kategori belirtilmelidir')
})

// Type exports
export type CreateCategoryInput = z.infer<typeof CreateCategorySchema>
export type UpdateCategoryInput = z.infer<typeof UpdateCategorySchema>
export type CategoryQueryInput = z.infer<typeof CategoryQuerySchema>
export type CategoryIdInput = z.infer<typeof CategoryIdSchema>
export type CategoryHierarchyInput = z.infer<typeof CategoryHierarchySchema>
export type BulkCategoryUpdateInput = z.infer<typeof BulkCategoryUpdateSchema>
export type MoveCategoryInput = z.infer<typeof MoveCategorySchema>
export type DuplicateCategoryInput = z.infer<typeof DuplicateCategorySchema>
export type ReorderCategoriesInput = z.infer<typeof ReorderCategoriesSchema>

{"name": "restoran-pos-desktop", "version": "1.0.0", "main": "electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "electron": "electron .", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && cross-env NODE_ENV=development electron .\"", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux"}, "build": {"appId": "com.yourcompany.restoran-pos", "productName": "Restoran POS", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "language": "1055"}}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.1", "electron-builder": "^26.0.12", "typescript": "^5.8.3", "vite": "^7.0.4", "wait-on": "^8.0.3"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-data-grid": "^8.8.0", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "dayjs": "^1.11.13", "i18next": "^25.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.6.0", "react-router-dom": "^7.6.3", "shared": "file:../shared", "zod": "^4.0.5", "zustand": "^5.0.6"}}